import React, {useEffect, useState} from 'react';
import {StatusBar, useColorScheme} from 'react-native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {ThemeProvider} from '@rneui/themed';
import {theme} from './src/utilities/theme';
import store, {persistor} from './src/store';
import {Provider} from 'react-redux';
import RootNavigation from './src/navigation/RootNavigation';
import {MenuProvider} from 'react-native-popup-menu';
import {PersistGate} from 'redux-persist/integration/react';
import {GestureHandlerRootView} from 'react-native-gesture-handler';

function App(): React.JSX.Element {
  const [isDark, setIsDark] = useState<boolean>(false);

  const colorScheme = useColorScheme();

  useEffect(() => {
    if (colorScheme === 'dark') {
      setIsDark(true);
    } else {
      setIsDark(false);
    }
  }, []);

  return (
    <Provider store={store}>
      <GestureHandlerRootView style={{flex: 1}}>
        <PersistGate loading={null} persistor={persistor}>
          <MenuProvider>
            <SafeAreaProvider>
              <ThemeProvider
                theme={{...theme, mode: isDark ? 'dark' : 'light'}}>
                <StatusBar
                  barStyle={'dark-content'}
                  backgroundColor={theme.lightColors?.background}
                />
                <RootNavigation />
              </ThemeProvider>
            </SafeAreaProvider>
          </MenuProvider>
        </PersistGate>
      </GestureHandlerRootView>
    </Provider>
  );
}

export default App;
