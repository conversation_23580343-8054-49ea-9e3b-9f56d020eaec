buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 21
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "25.1.8937393"
        kotlinVersion = "1.8.0"
    }
    repositories {
        google()
        mavenCentral()
        jcenter()
        maven { url "$rootDir/../node_modules/react-native/android" }
        maven { url 'https://maven.google.com' }
        maven { url "https://www.jitpack.io" }

    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath('com.google.gms:google-services:4.4.2')
    }
}

apply plugin: "com.facebook.react.rootproject"
