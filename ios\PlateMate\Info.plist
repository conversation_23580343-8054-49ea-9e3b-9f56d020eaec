<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>PlateMate</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>**************</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSTemporaryExceptionMinimumTLSVersion</key>
				<string>TLSv1.1</string>
			</dict>
			<key>api.theplatemate.ca</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Camera permission required to upload images</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Permission required to allow user send voice notes in chat</string>
	<key>NSHealthShareUsageDescription</key>
	<string>Reason string goes here</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>Reason string goes here</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string/>
	<key>NSMotionUsageDescription</key>
	<string>Reason string goes here</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Using to pick image from gallery</string>
	<key>UIAppFonts</key>
	<array>
		<string>Nunito-Black.ttf</string>
		<string>Nunito-BlackItalic.ttf</string>
		<string>Nunito-Bold.ttf</string>
		<string>Nunito-BoldItalic.ttf</string>
		<string>Nunito-ExtraBold.ttf</string>
		<string>Nunito-ExtraBoldItalic.ttf</string>
		<string>Nunito-ExtraLight.ttf</string>
		<string>Nunito-ExtraLightItalic.ttf</string>
		<string>Nunito-Italic.ttf</string>
		<string>Nunito-Light.ttf</string>
		<string>Nunito-LightItalic.ttf</string>
		<string>Nunito-Medium.ttf</string>
		<string>Nunito-MediumItalic.ttf</string>
		<string>Nunito-Regular.ttf</string>
		<string>Nunito-SemiBold.ttf</string>
		<string>Nunito-SemiBoldItalic.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>Inter-Bold.otf</string>
		<string>Inter-Light-BETA.otf</string>
		<string>Inter-Medium.otf</string>
		<string>Inter-Regular.otf</string>
		<string>Inter-SemiBold.otf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
