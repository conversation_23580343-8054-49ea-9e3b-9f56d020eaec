const PNGIcons = {
  HomeIcon: require('./HomeIcon.png'),
  Message: require('./Message.png'),
  SolarUser: require('./SolarUser.png'),
  SolarUser2: require('./SolarUser2.png'),
  TeaCups: require('./TeaCups.png'),
  User: require('./User.png'),
  AddIcon: require('./AddIcon.png'),
  PdfIcon: require('./PdfIcon.png'),
  Conversation: require('./Conversation.png'),
  HistoryIcon: require('./HistoryIcon.png'),
  WeightTracker: require('./WeightTracker.png'),
  ArrowRight: require('./ArrowRight.png'),
  ArrowDown: require('./ArrowDown.png'),
  BreakFast: require('./BreakFast.png'),
  Snacks: require('./Snacks.png'),
  TickIcon: require('./TickIcon.png'),
  ChatSendIcon: require('./ChatSendIcon.png'),
  EmojiIcon: require('./EmojiIcon.png'),
  AttachmentIcon: require('./AttachmentIcon.png'),
  ManFront: require('./ManFront.png'),
  ManBack: require('./ManBack.png'),
  ManSide: require('./ManSide.png'),
  Next: require('./Next.png'),
  NextDisabled: require('./NextDisabled.png'),
  UploadImage: require('./UploadImage.png'),
  ForkKnife: require('./ForkKnife.png'),
  Burn: require('./Burn.png'),
  ArrowLeft: require('./ArrowLeft.png'),
  PlusIcon: require('./PlusIcon.png'),
  Lunch: require('./Lunch.png'),
  Dinner: require('./Dinner.png'),
  CameraIcon: require('./CameraIcon.png'),
  EditIcon: require('./EditIcon.png'),
  ScanIcon: require('./ScanIcon.png'),
  EditPencil: require('./EditPencil.png'),
  CrossIcon: require('./CrossIcon.png'),
  TickFill: require('./TickFill.png'),
  DownloadIcon: require('./DownloadIcon.png'),
  WaterDrop: require('./WaterDrop.png'),
  FullGlass: require('./FullGlass.png'),
  EmptyGlass: require('./EmptyGlass.png'),
  Steps: require('./Steps.png'),
  ActivityIcon: require('./ActivityIcon.png'),
  TimeIcon: require('./TimeIcon.png'),
  RedIcon: require('./RedIcon.png'),
  CaptureIcon: require('./CaptureIcon.png'),
  Camera: require('./Camera.png'),
  Gallery: require('./Gallery.png'),
  TickFillLarge: require('./TickFill2.png'),
  ArrowUpIcon: require('./ArrowUp.png'),
};

export default PNGIcons;
