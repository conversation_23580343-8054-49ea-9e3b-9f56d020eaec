import React, {memo, useCallback, useRef, useState} from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  Dimensions,
  Text,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from 'react-native';

const {width} = Dimensions.get('window');
const ITEM_HEIGHT = 70;
const VISIBLE_ITEMS = 6;
const PADDING = 2;

const AgePicker = ({
  setCurrentVal,
  data,
}: {
  setCurrentVal: (val: string) => void;
  data: any;
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);

  // Memoize scroll handler
  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const offsetY = event.nativeEvent.contentOffset.y;
      const index = Math.round(offsetY / ITEM_HEIGHT);
      const clampedIndex = Math.max(0, Math.min(index, data.length - 1));
      setActiveIndex(clampedIndex);
      setCurrentVal(data[clampedIndex]);
    },
    [data, setCurrentVal],
  );

  // Memoize renderItem with useCallback
  const renderItem = useCallback(
    ({item, index}: {item: string; index: number}) => (
      <PickerItem item={item} index={index} activeIndex={activeIndex} />
    ),
    [activeIndex],
  );

  return (
    <View style={styles.container}>
      <View style={styles.pickerWrapper}>
        <FlatList
          ref={flatListRef}
          data={data}
          keyExtractor={(item, index) => item + index} // Add index to key
          showsVerticalScrollIndicator={false}
          bounces={false}
          snapToInterval={ITEM_HEIGHT}
          decelerationRate="fast"
          onScroll={handleScroll}
          scrollEventThrottle={32} // Reduced from 16 to 32
          contentContainerStyle={{
            paddingVertical: ITEM_HEIGHT * PADDING,
          }}
          getItemLayout={(_, index) => ({
            length: ITEM_HEIGHT,
            offset: ITEM_HEIGHT * index,
            index,
          })}
          renderItem={renderItem}
          initialNumToRender={20} // Render enough initial items
          windowSize={21} // Window size + 1
          maxToRenderPerBatch={10}
          updateCellsBatchingPeriod={50}
        />

        {/* Black pill overlay */}
        <View style={styles.blackPill} pointerEvents="none">
          <Text style={styles.activeText}>{data[activeIndex]}</Text>
        </View>
      </View>
    </View>
  );
};
// Memoized item component
const PickerItem = memo(
  ({
    item,
    index,
    activeIndex,
  }: {
    item: string;
    index: number;
    activeIndex: number;
  }) => {
    const isActive = index === activeIndex;

    return (
      <View style={[styles.item, isActive && styles.activeItem]}>
        <Text
          style={[
            styles.text,
            isActive ? styles.hiddenText : styles.inactiveText,
          ]}>
          {item}
        </Text>
      </View>
    );
  },
  (prev, next) =>
    prev.activeIndex === next.activeIndex && prev.item === next.item,
);

// Export memoized component
export default memo(AgePicker);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  pickerWrapper: {
    height: ITEM_HEIGHT * VISIBLE_ITEMS,
    width: 280,
    position: 'relative',
    overflow: 'hidden',
  },
  item: {
    height: ITEM_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeItem: {
    height: ITEM_HEIGHT * 1.87,
  },
  text: {
    fontWeight: 'bold',
  },
  inactiveText: {
    fontSize: 50,
    color: '#9EA0A5',
  },
  hiddenText: {
    fontSize: 50,
    color: '#9EA0A5',
  },
  blackPill: {
    position: 'absolute',
    top: ITEM_HEIGHT * 2, // center
    height: 130,
    width: 280,
    backgroundColor: '#000',
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    borderWidth: 4,
    borderColor: '#9EA0A5',
  },
  activeText: {
    fontSize: 80,
    color: '#fff',
    fontWeight: 'bold',
  },
});
