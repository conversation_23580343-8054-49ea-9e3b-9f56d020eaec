import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
  TextStyle,
} from 'react-native';
import React, {ReactNode} from 'react';
import {Fonts, theme} from '../../utilities/theme';

interface Props {
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  label?: string;
  onPress?: () => void;
  LeftIconContainer?: ViewStyle;
  rightIconContainer?: ViewStyle;
  labelStyle?: TextStyle;
  containerStyle?: ViewStyle;
  disabled?: boolean;
  hitSlop?: object;
}
const TextButtonwithIcon: React.FC<Props> = ({
  label,
  onPress,
  rightIcon,
  leftIcon,
  LeftIconContainer,
  rightIconContainer,
  labelStyle,
  containerStyle,
  disabled,
  hitSlop,
}) => {
  return (
    <TouchableOpacity
      hitSlop={hitSlop}
      disabled={disabled}
      style={[styles.container, containerStyle]}
      onPress={onPress}>
      <View style={LeftIconContainer}>{leftIcon}</View>
      <Text style={[styles.label, labelStyle]}>{label}</Text>
      <View style={rightIconContainer}>{rightIcon}</View>
    </TouchableOpacity>
  );
};

export default TextButtonwithIcon;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  label: {
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
  },
});
