import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../utilities/theme';
interface props {
  date: string;
}

const DateDivider: React.FC<props> = ({date}) => {
  return (
    <View style={{flexDirection: 'row', marginTop: 14}}>
      <Text style={styles.dividerText}>{date}</Text>
      <View style={styles.divider} />
    </View>
  );
};

export default DateDivider;

const styles = StyleSheet.create({
  dividerText: {
    color: theme.lightColors?.divider,
    fontSize: 12,
    lineHeight: 16,
    fontFamily: Fonts.regular,
  },
  divider: {
    borderTopWidth: 1,
    flexDirection: 'row',
    width: '85%',
    alignSelf: 'center',
    marginLeft: 5,
    borderTopColor: theme.lightColors?.divider,
  },
});
