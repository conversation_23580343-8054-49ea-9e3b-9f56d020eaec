import {Platform, StyleSheet, Text, View} from 'react-native';
import React, {useRef} from 'react';
import {Fonts, theme} from '../../utilities/theme';
import LottieView from 'lottie-react-native';
import Button from '../Button';
import {useTranslation} from 'react-i18next';

interface Props {
  title: string;
  paragraph: string;
  onPress: () => void;
}
const Feedback: React.FC<Props> = ({title, paragraph, onPress}) => {
  const animationRef = useRef<LottieView>(null);
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <View style={{alignItems: 'center'}}>
        {/* IMAGES */}
        <LottieView
          source={require('../../assets/successFeedback.json')}
          autoPlay
          ref={animationRef}
          loop
          style={styles.lottieStyle}
        />
        {/* TITLE */}
        <Text style={styles.title}>{title}</Text>
        {/* PARAGRAPH */}
        <Text style={styles.paragraph}>{paragraph}</Text>
      </View>
      <Button
        title={t('Continue')}
        onPress={onPress}
        containerStyle={{marginHorizontal: 4, marginBottom: 10}}
      />
    </View>
  );
};

export default Feedback;

const styles = StyleSheet.create({
  container: {
    paddingTop: 100,
    justifyContent: 'space-between',
    flex: 1,
    paddingBottom: Platform.OS === 'android' ? 40 : 20,
  },
  title: {
    fontSize: 20,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.bold,
    paddingTop: 24,
  },
  paragraph: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.regular,
    paddingTop: 12,
    textAlign: 'center',
  },
  lottieStyle: {zIndex: 2, width: 240, height: 240},
});
