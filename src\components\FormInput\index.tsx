import {StyleSheet, TouchableOpacity, Animated, TextStyle} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Input, InputProps} from '@rneui/themed';
import {Fonts, theme} from '../../utilities/theme';
import {EditIcon, Eye, Eye_off} from '../../assets/svgIcons';
import {Text} from 'react-native';

interface Props extends InputProps {
  isPassword?: boolean;
  onRightIconPress?: () => void;
  editIcon?: boolean;
  textinputStyles?: TextStyle;
  unitText?: string;
  disabledInputStyle?: TextStyle;
}

const FormInput: React.FC<Props> = ({
  isPassword,
  editIcon,
  textinputStyles,
  unitText,
  secureTextEntry,
  labelStyle,
  containerStyle,
  onRightIconPress,
  value,
  errorMessage,
  disabledInputStyle,
  ...rest
}) => {
  const [shakeAnimation] = useState(new Animated.Value(0));

  useEffect(() => {
    if (errorMessage) {
      startShakeAnimation();
    }
  }, [errorMessage]);

  const startShakeAnimation = () => {
    Animated.sequence([
      Animated.timing(shakeAnimation, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: -10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };
  return (
    <Animated.View style={{transform: [{translateX: shakeAnimation}]}}>
      <Input
        labelStyle={[styles.labelStyles, labelStyle]}
        errorStyle={{color: 'red'}}
        errorMessage={errorMessage}
        disabledInputStyle={disabledInputStyle}
        style={[styles.style, textinputStyles]}
        containerStyle={[styles.containerStyles, containerStyle]}
        value={value}
        rightIconContainerStyle={{
          alignSelf: 'flex-start',
        }}
        rightIcon={
          isPassword ? (
            secureTextEntry ? (
              <TouchableOpacity onPress={onRightIconPress}>
                <Eye_off
                  width={20}
                  height={19}
                  stroke={theme.lightColors?.grey4}
                />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity onPress={onRightIconPress}>
                <Eye width={20} height={19} stroke={theme.lightColors?.grey4} />
              </TouchableOpacity>
            )
          ) : editIcon ? (
            <TouchableOpacity onPress={onRightIconPress}>
              <EditIcon />
            </TouchableOpacity>
          ) : unitText ? (
            <Text style={styles.textUnit}>{unitText}</Text>
          ) : undefined
        }
        inputContainerStyle={[
          styles.inputContainerStyle,
          {
            borderColor: theme.lightColors?.grey1,
          },
        ]}
        {...rest}
        secureTextEntry={secureTextEntry}
      />
    </Animated.View>
  );
};

export default FormInput;

const styles = StyleSheet.create({
  labelStyles: {
    fontFamily: Fonts.light,
    fontSize: 14,
    color: theme.lightColors?.primary,
    fontWeight: '400',
    textAlignVertical: 'top',
  },
  inputContainerStyle: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    paddingHorizontal: 12,
    borderRadius: 10,
    marginTop: 8,
    backgroundColor: theme.lightColors?.grey5,
  },
  style: {
    fontSize: 14,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.secondary,
  },
  containerStyles: {
    paddingHorizontal: 0,
    marginVertical: 0,
  },
  textUnit: {
    fontFamily: Fonts.regular,
    fontSize: 14,
    color: theme.lightColors?.grey4,
    marginRight: 12,
  },
});
