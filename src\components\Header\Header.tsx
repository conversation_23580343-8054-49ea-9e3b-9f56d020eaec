import {StyleSheet, TouchableOpacity} from 'react-native';
import React from 'react';
import {ListItem} from '@rneui/themed';
import {Avatar} from '@rneui/base';
import {Fonts, sizes, theme} from '../../utilities/theme';
import {
  Bell,
  Bucket,
  DoNotDisturbIconInActive,
  DoNotDisturbIconActive,
  Bell2,
  BellWithDot,
} from '../../assets/svgIcons';
import {useAppSelector} from '../../store';
import PNGIcons from '../../assets/pngIcons';
import {useTranslation} from 'react-i18next';

interface Props {
  showDeleteButton?: boolean;
  doNotDisturb?: boolean;
  onBellPress: () => void;
  onDeletePress?: () => void;
  onAvatarPress: () => void;
  onDoNotDisturbPress?: () => void;
  showDoNotDisturbButton?: boolean;
  unread?: boolean;
}

const Header: React.FC<Props> = ({
  showDeleteButton,
  onBellPress,
  onDoNotDisturbPress,
  onDeletePress,
  onAvatarPress,
  doNotDisturb,
  showDoNotDisturbButton,
  unread,
}) => {
  const {t} = useTranslation();
  const userData = useAppSelector(state => state.user.user);

  const userProfilePicture = () => {
    if (userData.userType === 'coach') {
      return userData.photo;
    } else if (userData.userType === 'client') {
      return userData.image;
    } else return '';
  };

  return (
    <ListItem containerStyle={styles.containerStyles}>
      <TouchableOpacity onPress={onAvatarPress}>
        <Avatar
          size={44}
          rounded
          source={
            userProfilePicture()
              ? {
                  uri: userProfilePicture(),
                }
              : PNGIcons.User
          }
          avatarStyle={{height: 44, width: 44}}
        />
      </TouchableOpacity>
      <ListItem.Content>
        <ListItem.Title style={styles.title}>{t('Hello')} 👋</ListItem.Title>
        <ListItem.Subtitle style={styles.name}>
          {userData.name}
        </ListItem.Subtitle>
      </ListItem.Content>

      {showDeleteButton && (
        <TouchableOpacity style={styles.headerIcon} onPress={onDeletePress}>
          <Bucket />
        </TouchableOpacity>
      )}
      {showDoNotDisturbButton && (
        <TouchableOpacity
          style={styles.headerIcon}
          onPress={onDoNotDisturbPress}>
          {doNotDisturb ? (
            <DoNotDisturbIconInActive width={24} height={24} />
          ) : (
            <DoNotDisturbIconActive width={24} height={24} />
          )}
        </TouchableOpacity>
      )}
      <TouchableOpacity style={styles.headerIcon} onPress={onBellPress}>
        {unread ? <BellWithDot /> : <Bell2 width={20} height={20} />}
      </TouchableOpacity>
    </ListItem>
  );
};

export default Header;

const styles = StyleSheet.create({
  containerStyles: {
    paddingVertical: 0,
    paddingHorizontal: 0,
    backgroundColor: 'transparent',
  },
  title: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
    fontFamily: Fonts.regular,
  },
  name: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
  },
  headerIcon: {
    backgroundColor: theme.lightColors?.grey6,
    borderRadius: 8,
    padding: 5,
    borderWidth: 1,
    borderColor: '#984B3850',
  },
});
