import React, {<PERSON>} from 'react';
import {StyleSheet} from 'react-native';
import {Skeleton, SkeletonProps} from '@rneui/base';

interface Props extends SkeletonProps {}

const LoadingSkeleton: FC<Props> = ({style, ...props}) => {
  return (
    <Skeleton {...props} animation="none" style={[styles.loading, style]} />
  );
};

export default LoadingSkeleton;

const styles = StyleSheet.create({
  loading: {
    backgroundColor: '#d3d3d3',
  },
});
