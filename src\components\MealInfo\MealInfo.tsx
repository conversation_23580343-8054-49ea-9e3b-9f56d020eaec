import React from 'react';
import {View, Text, Image, StyleSheet} from 'react-native';
import images from '../../assets/images';
import {Fonts, theme} from '../../utilities/theme';
import PNGIcons from '../../assets/pngIcons';
import {useTranslation} from 'react-i18next';
import {IMealPost, IMealReview} from '../../interfaces/IMeal';
import {Divider} from '@rneui/base';
import {DotIcon} from '../../assets/svgIcons';

interface MealInfoProps {
  clientName: string;
  time: string;
  mealType: string;
  mealDetails: string[];
  totalCalories: number;
  proteinAmount: string;
  carbAmount: string;
  fatAmount: string;
  imageURI: string;
  review?: IMealReview | null;
  mealPost: IMealPost;
}

const MealInfo: React.FC<MealInfoProps> = ({
  clientName,
  time,
  mealType,
  mealDetails,
  totalCalories,
  proteinAmount,
  carbAmount,
  fatAmount,
  imageURI,
  review,
  mealPost,
}) => {
  const {t} = useTranslation();
  const mealIconType = () => {
    if (mealType.toLowerCase() === 'breakfast') {
      return PNGIcons.BreakFast;
    } else if (mealType.toLowerCase() === 'lunch') {
      return PNGIcons.Lunch;
    } else if (mealType.toLowerCase() === 'dinner') {
      return PNGIcons.Dinner;
    } else if (mealType.toLowerCase() === 'snacks') {
      return PNGIcons.Snacks;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Image
          source={imageURI ? {uri: imageURI} : images.HappyClient}
          style={styles.clientImage}
        />
        <View style={styles.clientInfo}>
          <Text style={styles.clientName}>{clientName}</Text>
          <Text style={styles.timeText}>{time}</Text>
        </View>
      </View>
      <View style={styles.mealTypeContainer}>
        <Image source={mealIconType()} style={styles.mealIcon} />
        <Text style={styles.mealType}>{mealType}</Text>
      </View>
      {mealDetails?.length ? (
        <>
          <Text style={styles.detailText}>{t('Details')}</Text>
          {mealDetails.map((detail, index) => (
            <>
              <View style={styles.detailDescriptionContainer} key={index}>
                <DotIcon width={5} height={5} style={styles.dotIcon} />
                <Text style={styles.detailDescription}>{detail}</Text>
              </View>
            </>
          ))}
        </>
      ) : null}
      <View style={styles.divider} />
      <Text style={styles.totalText}>
        Total:{' '}
        <Text style={styles.caloryAmount}>
          {totalCalories} <Text style={{fontSize: 14}}>{t('cal')}</Text>
        </Text>
      </Text>
      <View style={styles.proteinInfoContainer}>
        <View
          style={[
            styles.proteinContainer,
            {backgroundColor: theme.lightColors?.grey1},
          ]}>
          <Text
            style={[styles.proteinText, {color: theme.lightColors?.primary}]}>
            {t('Protein')}
          </Text>
          <Text
            style={[styles.proteinAmount, {color: theme.lightColors?.primary}]}>
            {proteinAmount}
          </Text>
        </View>
        <View
          style={[
            styles.proteinContainer,
            {backgroundColor: theme.lightColors?.grey1},
          ]}>
          <Text
            style={[styles.proteinText, {color: theme.lightColors?.primary}]}>
            {t('Carbs')}
          </Text>
          <Text
            style={[styles.proteinAmount, {color: theme.lightColors?.primary}]}>
            {carbAmount}
          </Text>
        </View>
        <View
          style={[
            styles.proteinContainer,
            {backgroundColor: theme.lightColors?.grey1},
          ]}>
          <Text
            style={[styles.proteinText, {color: theme.lightColors?.primary}]}>
            {t('Fat')}
          </Text>
          <Text
            style={[styles.proteinAmount, {color: theme.lightColors?.primary}]}>
            {fatAmount}
          </Text>
        </View>
      </View>
      {review?.text ? (
        <View>
          <Divider
            style={{marginVertical: 12}}
            color={theme.lightColors?.grey2}
          />
          <Text style={styles.name}>{t('Reviews')}</Text>
          <Text style={styles.review}>{review?.text}</Text>
        </View>
      ) : null}
      {review?.text ? (
        <View style={styles.reviewedContainer}>
          <Text style={styles.reviewedText}>{t('Reviewed')}</Text>
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    padding: 12,
    marginTop: 12,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  clientImage: {
    height: 40,
    width: 40,
    borderRadius: 100,
  },
  clientInfo: {
    justifyContent: 'space-between',
    marginLeft: 10,
  },
  clientName: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    color: theme.lightColors?.black,
  },
  timeText: {
    fontSize: 12,
    fontFamily: Fonts.regular,
    color: '#2C2C2E80',
  },
  mealTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 14,
  },
  mealIcon: {
    width: 20,
    height: 20,
    marginRight: 5,
  },
  mealType: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    color: theme.lightColors?.black,
  },
  detailText: {
    fontFamily: Fonts.regular,
    color: '#2C2C2EB2',
    marginTop: 12,
  },
  detailDescriptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  dotIcon: {
    marginRight: 10,
  },
  detailDescription: {
    fontFamily: Fonts.medium,
    fontSize: 14,
    color: theme.lightColors?.black,
  },
  divider: {
    borderTopColor: theme.lightColors?.grey2,
    borderTopWidth: 1,
    marginVertical: 18,
    alignSelf: 'center',
    width: '100%',
  },
  totalText: {
    fontFamily: Fonts.regular,
    fontSize: 12,
    color: theme.lightColors?.grey4,
    textAlign: 'center',
  },
  caloryAmount: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.primary,
  },
  proteinContainer: {
    paddingVertical: 6,
    paddingHorizontal: 6,
    borderRadius: 8,
    width: 70,
    alignItems: 'center',
  },
  proteinText: {
    fontFamily: Fonts.regular,
    fontSize: 14,
  },
  proteinAmount: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
  },
  proteinInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 30,
    marginTop: 14,
  },
  reviewedContainer: {
    backgroundColor: theme.lightColors?.white,
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 6,
    marginTop: 20,
    alignSelf: 'flex-end',
    alignItems: 'center',
    borderColor: theme.lightColors?.black,
    borderWidth: 1,
  },
  reviewedText: {
    fontFamily: Fonts.medium,
    color: theme.lightColors?.black,
    fontSize: 14,
  },
  review: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
    textTransform: 'capitalize',
    marginTop: 5,
  },
  trashBtn: {
    borderRadius: 4,
    padding: 3,
    backgroundColor: theme.lightColors?.error,
  },
  buttonContainer: {justifyContent: 'flex-end', flexDirection: 'row'},
  editIcon: {width: 16, height: 16},
  editBtn: {
    borderColor: theme.lightColors?.primary,
    borderWidth: 1,
    borderRadius: 4,
    padding: 2,
    marginRight: 8,
  },
  name: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
  },
});

export default MealInfo;
