import {
  ActivityIndicator,
  Image,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import Modal from 'react-native-modal';
import {theme} from '../../../utilities/theme';
import {ListItem} from '@rneui/base';
import FormInput from '../../FormInput';
import moment from 'moment';
import {useFormik} from 'formik';
import * as yup from 'yup';
import PNGIcons from '../../../assets/pngIcons';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';

interface Props {
  isModalVisible: boolean;
  setModalVisible: (isModalVisible: boolean) => void;
  clientName: string;
  clientImage: string;
  mealCreatedAt: string;
  onSubmitReview: (text: string) => void;
  isLoading: boolean;
  reviewText?: string;
}

interface FormData {
  text: string;
}

const reviewSchema = yup.object({
  text: yup.string().required('Review Text is required'),
});

const AddReviewModal: React.FC<Props> = ({
  isModalVisible,
  setModalVisible,
  clientImage,
  mealCreatedAt,
  clientName,
  onSubmitReview,
  isLoading,
  reviewText,
}) => {
  const {t} = useTranslation();

  const formik = useFormik({
    initialValues: {
      text: reviewText || '',
    },
    onSubmit: (formData: FormData) => {
      onSubmitReview(formData.text);
      formik.resetForm({
        values: {text: ''},
        touched: {text: false},
      });
    },
    validationSchema: reviewSchema,
    enableReinitialize: true,
  });

  return (
    <Modal
      isVisible={isModalVisible}
      animationIn={'zoomIn'}
      animationOut={'zoomOut'}
      animationInTiming={500}
      animationOutTiming={500}
      onBackdropPress={() => setModalVisible(false)}
      backdropOpacity={0.3}
      avoidKeyboard>
      <View style={styles.modalContainer}>
        <ListItem containerStyle={styles.modalListItem}>
          <Image
            style={styles.clientImage}
            source={clientImage ? {uri: clientImage} : PNGIcons.User}
          />
          <ListItem.Content style={styles.listContent}>
            <ListItem.Title style={styles.name}>{clientName}</ListItem.Title>
            <ListItem.Title style={styles.dateText}>
              {moment(+mealCreatedAt).isSame(moment(), 'day')
                ? `Today - ${moment(+mealCreatedAt).format('hh:mm a')}`
                : moment(+mealCreatedAt).format('MMM DD - hh:mm a')}
            </ListItem.Title>
          </ListItem.Content>
        </ListItem>
        <FormInput
          label={t('Add Review')}
          multiline
          containerStyle={{marginTop: 16}}
          textinputStyles={styles.inputHeight}
          textAlignVertical="top"
          onChangeText={formik.handleChange('text')}
          onBlur={formik.handleBlur('text')}
          errorMessage={
            (formik.touched.text && t(formik.errors.text as string)) ||
            undefined
          }
          value={formik.values.text}
        />
        <View style={styles.rowContainer}>
          <TouchableOpacity
            style={{marginRight: 40}}
            onPress={() => {
              setModalVisible(false);
              formik.resetForm({
                values: {text: ''},
                touched: {text: false},
              });
            }}>
            <Text style={styles.cancelText}>{t('Cancel')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => formik.handleSubmit()}
            disabled={isLoading}
            style={styles.rowButtonContainer}>
            <Text style={styles.saveText}>{t('Save')}</Text>
            {isLoading && (
              <ActivityIndicator
                size={'small'}
                color={theme.lightColors?.primary}
                style={styles.activityIndicator}
              />
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default AddReviewModal;
