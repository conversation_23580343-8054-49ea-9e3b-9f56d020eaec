import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import Modal from 'react-native-modal';
import {Button} from '../..';
import {Fonts, theme} from '../../../utilities/theme';
import {useTranslation} from 'react-i18next';

interface Props {
  visible: boolean;
  onCancel: () => void;
  primaryButtonOnpress: () => void;
  topRedTitle: string;
  description: string;
  primaryButtonName: string;
  secondaryButtonText: string;
  loading?: boolean;
  disabled?: boolean;
}

const GeneralModal: React.FC<Props> = ({
  visible,
  onCancel,
  primaryButtonOnpress,
  topRedTitle,
  description,
  primaryButtonName,
  secondaryButtonText,
  disabled,
  loading,
}) => {
  const {t} = useTranslation();

  return (
    <Modal
      isVisible={visible}
      animationIn={'zoomIn'}
      animationOut={'zoomOut'}
      animationInTiming={500}
      animationOutTiming={500}
      backdropOpacity={0.6}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.title}>{topRedTitle}</Text>
          <View style={styles.divider} />
          <Text style={styles.text}>{description}</Text>
          <Text style={[styles.text, styles.confirmText]}>
            {t('Are you sure?')}
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
              <Text style={styles.cancelText}>{secondaryButtonText}</Text>
            </TouchableOpacity>
            <Button
              title={primaryButtonName}
              onPress={primaryButtonOnpress}
              containerStyle={styles.deleteButton}
              loading={loading}
              disabled={disabled}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default GeneralModal;

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    alignItems: 'center',
    width: '95%',
    paddingBottom: 18,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    color: theme.darkColors?.error,
    fontFamily: Fonts.semiBold,
    fontSize: 18,
    lineHeight: 24,
    marginTop: 16,
  },
  divider: {
    borderTopWidth: 1,
    width: '100%',
    borderTopColor: '#DDDDDD',
    marginVertical: 12,
  },
  text: {
    fontSize: 16,
    lineHeight: 22,
    fontFamily: Fonts.semiBold,
    width: '80%',
    textAlign: 'center',
    color: theme.lightColors?.black,
  },
  confirmText: {
    fontFamily: Fonts.regular,
    marginTop: 10,
    marginBottom: 14,
    color: theme.lightColors?.black,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  cancelButton: {
    paddingVertical: 12,
    marginRight: 8,
    width: '40%',
    alignItems: 'center',
  },
  deleteButton: {
    paddingVertical: 10,
    width: '85%',
    backgroundColor: theme.lightColors?.error,
  },
  cancelText: {
    color: theme.lightColors?.secondary,
  },
});
