import {StyleSheet, Text, View} from 'react-native';
import React, {useRef} from 'react';
import Modal from 'react-native-modal';
import LottieView from 'lottie-react-native';
import {Fonts, theme} from '../../../utilities/theme';

interface Props {
  isModalVisible: boolean;
  setModalVisible: (isModalVisible: boolean) => void;
  paragraph: string;
}

const ModalSuccess: React.FC<Props> = ({
  isModalVisible,
  setModalVisible,
  paragraph,
}) => {
  const animationRef = useRef<LottieView>(null);

  return (
    <Modal
      isVisible={isModalVisible}
      animationIn={'zoomIn'}
      animationOut={'zoomOut'}
      animationInTiming={500}
      animationOutTiming={500}
      onBackdropPress={() => setModalVisible(false)}
      backdropOpacity={0.3}>
      <View style={styles.modalContainer}>
        <LottieView
          source={require('../../../assets/successFeedback.json')}
          autoPlay
          ref={animationRef}
          loop
          style={styles.lottieStyle}
        />
        <Text style={styles.title}>{paragraph}</Text>
      </View>
    </Modal>
  );
};

export default ModalSuccess;

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: theme.lightColors?.white,
    borderRadius: 20,
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.regular,
    textAlign: 'center',
    paddingTop: 20,
  },
  lottieStyle: {zIndex: 2, width: 60, height: 60},
});
