import {
  ActivityIndicator,
  Alert,
  Image,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {Fonts, theme} from '../../utilities/theme';
import PNGIcons from '../../assets/pngIcons';
import {CloseIcon, DownloadIcon} from '../../assets/svgIcons';
import RNFetchBlob from 'rn-fetch-blob';
import Toast from 'react-native-toast-message';
import RNFS from 'react-native-fs';
import {useTranslation} from 'react-i18next';
import <PERSON><PERSON>iewer from 'react-native-file-viewer';

interface props {
  details?: boolean;
  fileName: string;
  fileUrl: string;
}

const PdfFileContainer: React.FC<props> = ({details, fileName, fileUrl}) => {
  const [isLoading, setLoading] = useState(false);
  const [isOpening, setIsOpening] = useState(false);

  const {t} = useTranslation();
  // DOWNLOAD PDF
  async function hanldeDownloadFile() {
    setLoading(true);

    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        );
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          Toast.show({type: 'error', text2: t('Permission Denied')});
          setLoading(false);
          return;
        }
      }

      const {dirs} = RNFetchBlob.fs;
      const path =
        Platform.OS === 'android'
          ? `${dirs.DownloadDir}/${fileName}.pdf`
          : `${RNFS.DocumentDirectoryPath}/${fileName}.pdf`;

      const downloadConfig =
        Platform.OS === 'android'
          ? {
              fileCache: true,
              addAndroidDownloads: {
                useDownloadManager: true,
                notification: true,
                path,
                description: 'Downloading file',
              },
            }
          : {fileCache: true, path};

      await RNFetchBlob.config(downloadConfig)
        .fetch('GET', fileUrl)
        .then(async () => {
          Toast.show({type: 'success', text2: t('Download Complete')});
        });
    } catch (error) {
      console.error('Error downloading file:', error);
    } finally {
      setLoading(false);
    }
  }
  const url = fileUrl;

  function getUrlExtension(url: any) {
    return url.split(/[#?]/)[0].split('.').pop().trim();
  }
  const extension = getUrlExtension(url);
  const localFile = `${RNFS.DocumentDirectoryPath}/${fileName}.${extension}`;
  const options = {
    fromUrl: url,
    toFile: localFile,
  };

  const openFIle = () => {
    setIsOpening(true);
    RNFS.downloadFile(options)
      .promise.then(() => FileViewer.open(localFile))
      .then(() => {
        // success
      })
      .catch(error => {
        // error
        console.log('Error opening pdf', error);
      })
      .finally(() => {
        setIsOpening(false);
      });
  };
  return (
    <TouchableOpacity style={styles.fileContainer} onPress={openFIle}>
      {isOpening ? (
        <ActivityIndicator
          color={theme.lightColors?.primary}
          style={{alignSelf: 'center'}}
        />
      ) : (
        <>
          <View style={styles.innerContainer}>
            <Image source={PNGIcons.PdfIcon} style={styles.fileImage} />
            <Text style={styles.fileNameText}>{fileName}</Text>
          </View>
          <TouchableOpacity onPress={hanldeDownloadFile} disabled={isLoading}>
            {isLoading ? (
              <ActivityIndicator color={theme.lightColors?.primary} />
            ) : details ? (
              <DownloadIcon />
            ) : (
              <CloseIcon />
            )}
          </TouchableOpacity>
        </>
      )}
    </TouchableOpacity>
  );
};

export default PdfFileContainer;

const styles = StyleSheet.create({
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    paddingVertical: 14,
    paddingHorizontal: 12,
    borderRadius: 10,
    justifyContent: 'space-between',
  },
  fileImage: {height: 32, width: 32, marginRight: 10},
  fileNameText: {
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.black,
    fontSize: 14,
    lineHeight: 19,
  },
  innerContainer: {flexDirection: 'row', alignItems: 'center'},
});
