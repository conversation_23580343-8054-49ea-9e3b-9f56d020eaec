import {StyleSheet, View, ViewStyle} from 'react-native';
import React from 'react';
import {Input} from '@rneui/base';
import {Fonts, theme} from '../../utilities/theme';
import {Magnify, MicBtn} from '../../assets/svgIcons';
import {useTranslation} from 'react-i18next';

interface Props {
  value?: string;
  onChangeText?: (text: string) => void;
  containerStyle?: ViewStyle;
  micBtn?: boolean;
  onFocus?: () => void;
}

const SearchBar: React.FC<Props> = ({
  onChangeText,
  value,
  containerStyle,
  micBtn,
  onFocus,
}) => {
  const {t} = useTranslation();

  return (
    <View>
      <Input
        placeholder={t('Search')}
        placeholderTextColor={'#A0A0A0'}
        value={value}
        onChangeText={onChangeText}
        onFocus={onFocus || undefined}
        style={styles.style}
        containerStyle={[styles.containerStyles, containerStyle]}
        leftIcon={<Magnify />}
        inputContainerStyle={[
          styles.inputContainerStyle,
          {
            borderColor: value
              ? theme.lightColors?.primary
              : theme.lightColors?.grey1,
          },
        ]}
        rightIcon={micBtn ? <MicBtn /> : undefined}
      />
    </View>
  );
};

export default SearchBar;

const styles = StyleSheet.create({
  labelStyles: {
    fontFamily: Fonts.light,
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontWeight: '400',
  },
  inputContainerStyle: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    paddingHorizontal: 12,
    borderRadius: 10,
    marginTop: 18,
    backgroundColor: theme.lightColors?.grey5,
  },
  style: {
    fontSize: 14,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.secondary,
    marginLeft: 8,
  },
  containerStyles: {
    paddingHorizontal: 0,
    marginVertical: 0,
  },
});
