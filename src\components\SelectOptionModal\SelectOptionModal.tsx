import {FC, useState} from 'react';
import {Pressable, StyleSheet, TouchableOpacity} from 'react-native';
import {Text, View} from 'react-native';
import Modal from 'react-native-modal';
import {Fonts, theme} from '../../utilities/theme';
import {useTranslation} from 'react-i18next';

interface Props {
  label: string;
  optionArray: string[];
  value: string;
  setValue: (val: string) => void;
  showReset?: boolean;
}

const SelectOptionModal: FC<Props> = ({
  label,
  optionArray,
  value,
  setValue,
  showReset,
}) => {
  const {t} = useTranslation();
  const [isVisible, setVisible] = useState(false);
  const handleCloseModal = (val: string) => {
    setValue(val);
    setVisible(false);
  };

  return (
    <>
      <Text style={[styles.selectLabel]}>{label}</Text>

      <TouchableOpacity
        style={styles.selectContainer}
        onPress={() => setVisible(true)}>
        <Text style={styles.selectText}>{value}</Text>
        {value && showReset ? (
          <Pressable onPress={() => setValue('')}>
            <Text style={styles.clearText}>clear</Text>
          </Pressable>
        ) : null}
      </TouchableOpacity>

      <Modal
        isVisible={isVisible}
        animationIn={'zoomIn'}
        animationOut={'zoomOut'}
        animationInTiming={500}
        animationOutTiming={500}
        onBackdropPress={() => setVisible(false)}
        backdropOpacity={0.3}
        avoidKeyboard
        style={{}}>
        <View
          style={{
            backgroundColor: theme.lightColors?.background,
            borderRadius: 12,
            // flex: 0.5,
          }}>
          <Text style={styles.selectHeading}>{label}</Text>

          {/* OPTIONS */}
          {optionArray.map((item, index) => {
            return (
              <View key={item}>
                <View style={styles.divider} />
                <TouchableOpacity
                  style={styles.selectOption}
                  onPress={() => handleCloseModal(item)}>
                  {value === item ? <View style={styles.activeOption} /> : null}
                  <Text style={styles.selectOptionText}>{t(item)}</Text>
                </TouchableOpacity>
              </View>
            );
          })}
        </View>
      </Modal>
    </>
  );
};

export default SelectOptionModal;

const styles = StyleSheet.create({
  selectContainer: {
    borderWidth: 1,
    borderRadius: 10,
    paddingVertical: 14,
    marginBottom: 24,

    borderColor: theme.lightColors?.grey1,
    paddingHorizontal: 12,
    marginTop: 8,
    backgroundColor: '#FAFAFA',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectText: {
    fontSize: 14,
    color: theme.lightColors?.black,
    fontFamily: Fonts.medium,
  },
  clearText: {
    fontSize: 12,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.light,
  },
  selectLabel: {
    fontSize: 14,
    fontFamily: Fonts.light,
    marginBottom: 10,
    fontWeight: '400',
    textAlignVertical: 'top',
    color: theme.lightColors?.secondary,
  },
  selectHeading: {
    fontSize: 16,
    fontFamily: Fonts.bold,
    padding: 20,
    textAlign: 'center',
    color: theme.lightColors?.primary,
  },
  divider: {
    borderTopColor: theme.lightColors?.grey1,
    borderTopWidth: 1,
  },
  selectOption: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectOptionText: {
    fontSize: 14,
    fontFamily: Fonts.medium,
    textTransform: 'capitalize',
    color: theme.lightColors?.primary,
  },
  activeOption: {
    height: 8,
    width: 8,
    backgroundColor: theme.lightColors?.primary,
    borderRadius: 2,
    marginRight: 16,
  },
});
