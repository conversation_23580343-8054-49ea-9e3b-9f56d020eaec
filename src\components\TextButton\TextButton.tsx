import {
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../utilities/theme';

interface Props {
  onPress?: () => void;
  containerStyle?: ViewStyle;
  titleStyle: TextStyle;
  title: string;
}
const TextButton: React.FC<Props> = ({
  onPress,
  titleStyle,
  containerStyle,
  title,
}) => {
  return (
    <TouchableOpacity onPress={onPress} style={containerStyle}>
      <Text style={[styles.text, titleStyle]}>{title}</Text>
    </TouchableOpacity>
  );
};

export default TextButton;

const styles = StyleSheet.create({
  text: {
    fontSize: 16,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
  },
});
