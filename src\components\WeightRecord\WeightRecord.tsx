import {Image, ImageSourcePropType, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../utilities/theme';
import {useTranslation} from 'react-i18next';
// import PNGIcons from '../../assets/pngIcons';

interface weightProps {
  weight: number;
  date: string;
  front: string;
  side: string;
  back: string;
}

const WeightRecord: React.FC<weightProps> = ({
  weight,
  date,
  front,
  back,
  side,
}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <View style={styles.dateWeightContainer}>
        <Text style={styles.weightText}>{weight} kg</Text>
        <Text style={styles.dateText}>{date}</Text>
      </View>
      <View style={styles.divider} />
      <View style={styles.dateWeightContainer}>
        <View>
          <Text style={styles.bodySide}>{t('Front')}</Text>
          <Image source={{uri: front}} style={styles.bodyImage} />
        </View>
        <View style={styles.verticalDivider} />
        <View>
          <Text style={styles.bodySide}>{t('Side')}</Text>
          <Image source={{uri: side}} style={styles.bodyImage} />
        </View>
        <View style={styles.verticalDivider} />
        <View>
          <Text style={styles.bodySide}>{t('Back')}</Text>
          <Image source={{uri: back}} style={styles.bodyImage} />
        </View>
      </View>
    </View>
  );
};

export default WeightRecord;

const styles = StyleSheet.create({
  container: {
    borderRadius: 10,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey2,
    padding: 12,
    marginTop: 16,
  },
  divider: {
    borderTopColor: theme.lightColors?.grey2,
    borderTopWidth: 1,
    marginVertical: 16,
    alignSelf: 'center',
    width: '100%',
  },
  verticalDivider: {
    borderRightColor: theme.lightColors?.grey2,
    borderRightWidth: 1,
    height: 98,
  },
  bodySide: {
    fontSize: 12,
    fontFamily: Fonts.semiBold,
    color: '#7D7D7D',
    marginBottom: 8,
  },
  dateWeightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  weightText: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    lineHeight: 19,
    color: '#A0A0A0',
  },
  dateText: {fontSize: 12, fontFamily: Fonts.regular, color: '#A0A0A0'},
  bodyImage: {height: 75, width: 75},
});
