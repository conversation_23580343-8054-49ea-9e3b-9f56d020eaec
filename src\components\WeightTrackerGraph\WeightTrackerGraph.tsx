import {Image, Text, TouchableOpacity, View} from 'react-native';
import React, {useMemo} from 'react';
import {LineChart, ruleTypes, lineDataItem} from 'react-native-gifted-charts';
import {theme} from '../../utilities/theme';
import PNGIcons from '../../assets/pngIcons';
import {styles} from './styles';
import {IWeight} from '../../interfaces/IWeight';
import moment from 'moment';
import {useTranslation} from 'react-i18next';
interface props {
  // this will be used for adding new weight
  onPress?: () => void;
  client?: boolean;
  view?: boolean;
  weights: IWeight[];
  // this will be used incase of existing weight
  onPressWeight?: (w: IWeight) => void;
}

const WeightTrackerGraph: React.FC<props> = ({
  onPress,
  client,
  view,
  weights,
  onPressWeight,
}) => {
  const {t} = useTranslation();

  const graphData = weights?.map(item => {
    return {
      date: moment.unix(+item.createdAt).format('DD/MM'),
      value: Number(item.value),
      id: item._id,
    };
  });

  const markedDates = weights?.map(item =>
    moment(+item.createdAt).format('DD/MM'),
  );
  // const yAxisLabelTexts = Array.from({length: 10}, (_, i) =>
  //   (i * 10).toString(),
  // );

  const {offset, maxValue} = useMemo(() => {
    if (!graphData.length) return {offset: 0, maxValue: 200};

    let min = Math.floor(Math.min(...graphData.map(d => d.value)) / 5) * 5;
    let max = Math.ceil(Math.max(...graphData.map(d => d.value)) / 5) * 5;

    // Ensure at least a 20-point range for better visibility
    if (max - min < 20) {
      min = max - 20;
    }

    return {
      offset: min,
      maxValue: max,
    };
  }, [graphData]);

  return (
    <View style={styles.mainGraphContainer}>
      {view ? (
        <TouchableOpacity
          style={styles.graphContainer}
          hitSlop={7}
          onPress={onPress}>
          <View style={styles.weightHeadingContainer}>
            <Image source={PNGIcons.WeightTracker} style={styles.textIcon} />
            <Text style={styles.weightHeadingText}>{t('Weight Tracking')}</Text>
          </View>
          <Image source={PNGIcons.ArrowRight} style={styles.trackingImage} />
        </TouchableOpacity>
      ) : client ? (
        <View style={styles.rowContainer}>
          <View style={styles.weightHeadingContainer}>
            <Image source={PNGIcons.WeightTracker} style={styles.textIcon} />
            <Text style={styles.weightHeadingText}>{t('Weight Tracking')}</Text>
          </View>
          <TouchableOpacity
            style={[styles.graphContainer, styles.graphContainerButton]}
            hitSlop={7}
            onPress={onPress}>
            <Image source={PNGIcons.PlusIcon} style={styles.plusIcon} />
          </TouchableOpacity>
        </View>
      ) : (
        <TouchableOpacity
          style={styles.graphContainer}
          hitSlop={7}
          onPress={onPress}>
          <View style={styles.graphContainer}>
            <View style={styles.weightHeadingContainer}>
              <Image source={PNGIcons.WeightTracker} style={styles.textIcon} />
              <Text style={styles.weightHeadingText}>
                {t('Weight Tracking')}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      )}
      {graphData ? (
        <LineChart
          data={graphData}
          width={308}
          color={'#000'}
          areaChart
          thickness={1}
          dataPointsColor="#000"
          dataPointsHeight={20}
          dataPointsRadius={5}
          yAxisLabelWidth={40}
          yAxisThickness={0}
          rulesType={ruleTypes.DOTTED}
          rulesColor={theme.lightColors?.grey1}
          yAxisColor="#666E75"
          showVerticalLines
          yAxisTextStyle={{color: '#666E75'}}
          xAxisLabelTextStyle={{color: '#666E75'}}
          xAxisLabelTexts={markedDates?.map(item => item)}
          xAxisColor={'#666E75'}
          yAxisTextNumberOfLines={2}
          curved
          yAxisOffset={offset - 5}
          stepValue={5}
          stepHeight={30}
          // yAxisLabelTexts={yAxisLabelTexts}
          maxValue={maxValue - offset + 10}
          onPress={(val: any) => {
            const found = weights.find(w => {
              return w._id === val.id;
            });
            if (found && onPressWeight) {
              onPressWeight(found);
            }
          }}
          // stepHeight={20}
          // stepValue={5}
        />
      ) : null}
    </View>
  );
};

export default WeightTrackerGraph;
