import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../utilities/theme';

export const styles = StyleSheet.create({
  weightHeadingText: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.black,
  },
  weightHeadingContainer: {flexDirection: 'row', alignItems: 'center'},
  graphContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 32,
  },
  mainGraphContainer: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 12,
    paddingTop: 28,
    paddingBottom: 20,
    paddingHorizontal: 10,
    marginTop: 20,
  },
  textIcon: {
    height: 18,
    width: 18,
    marginRight: 12,
    tintColor: theme.lightColors?.grey0,
  },
  trackingImage: {
    width: 20,
    height: 20,
    tintColor: theme.lightColors?.black,
  },
  graphContainerButton: {
    backgroundColor: theme.lightColors?.black,
    width: 30,
    height: 30,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 0,
  },
  plusIcon: {
    width: 14,
    height: 14,
    tintColor: '#FFEDD7',
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 25,
  },
});
