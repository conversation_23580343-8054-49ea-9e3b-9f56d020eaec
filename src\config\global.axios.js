import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
const AppInstance = axios.create({
  // baseURL: 'https://1fc5-2402-ad80-13d-cebf-e888-d824-4fdd-de3b.ngrok-free.app/api',
  baseURL: 'https://api.theplatemate.ca/api',
});

AppInstance.interceptors.request.use(async request => {
  const access_token = await AsyncStorage.getItem('access_token');
  const BearerToken = `Bearer ${access_token ? access_token : true}`;
  if (access_token) {
    request.headers.Authorization = BearerToken;
  }

  return request;
});

AppInstance.interceptors.response.use(response => {
  return response;
});

export default AppInstance;
