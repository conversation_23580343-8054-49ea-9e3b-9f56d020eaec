import {useState} from 'react';
import {useAppDispatch} from '../../store';
import {useAppSelector} from '../../store';

import {IQueryResponse} from '../../interfaces/QueryResponse';

import {setChats, updateUnreadCounter} from '../../store/chatsSlice';

import AppInstance from '../../config/global.axios';
import {ICreateExercise, IExercise} from '../../interfaces/IExercise';
import {IActivity, ICreateActivity} from '../../interfaces/IActivity';
import {AxiosResponse} from 'axios';
import {setDailyActivity} from '../../store/userSlice';

const useActivity = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmittingLogs, setIsSubmittingLog] = useState(false);
  const [isSubmittingExercise, setIsSubmittingExercise] = useState(false);
  const {user} = useAppSelector(state => state.user);
  const dispatch = useAppDispatch();

  const createExercise = async (payload: ICreateExercise) => {
    try {
      setIsSubmittingExercise(true);

      const response: AxiosResponse<IExercise> = await AppInstance({
        //    url: `/exercises/exercise-logs`,
        url: `/exercises`,

        method: 'POST',
        data: payload,
      });
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      setIsSubmittingExercise(false);
    }
  };

  const fetchDailyActivity = async (
    clientId: string,
    date: Date,
    storeInRedux = true,
  ) => {
    try {
      setIsLoading(true);

      const response: AxiosResponse<[IActivity]> = await AppInstance({
        url: `/exercises/activity-logs/${clientId}?date=${date}`,
        method: 'GET',
      });
      if (storeInRedux) {
        const [activity] = response.data;

        dispatch(setDailyActivity(activity));
      }
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  //   Add activity performed by user
  const addDailyActivityLog = async (payload: ICreateActivity) => {
    try {
      setIsSubmittingLog(true);

      const response: AxiosResponse<IActivity> = await AppInstance({
        url: `/exercises/activity-logs`,
        method: 'POST',
        data: payload,
      });
      // Updated daily activity will be returned
      const activity = response.data;
      dispatch(setDailyActivity(activity));
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      setIsSubmittingLog(false);
    }
  };

  const deleteExerciseFromDailyLog = async (
    clientId: string,
    exerciseId: string,
    date: Date,
  ) => {
    try {
      setIsSubmittingLog(true);

      const response: AxiosResponse<IActivity> = await AppInstance({
        url: `/exercises/activity-logs/${clientId}?exerciseId=${exerciseId}&date=${date}`,
        method: 'DELETE',
      });

      // Updated daily activity will be returned
      const activity = response.data;
      dispatch(setDailyActivity(activity));

      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    } finally {
      setIsSubmittingLog(false);
    }
  };

  return {
    // socket,
    createExercise,
    addDailyActivityLog,
    fetchDailyActivity,
    isLoading,
    isSubmittingLogs,
    isSubmittingExercise,
    deleteExerciseFromDailyLog,
  };
};

export default useActivity;
