import {useCallback, useEffect, useState} from 'react';
import {useAppDispatch} from '../../store';
import {useAppSelector} from '../../store';
import {AxiosResponse} from 'axios';
import {IQueryResponse} from '../../interfaces/QueryResponse';
import {IChat} from '../../interfaces/IChat';
import ChatServices from '../../services/ChatServices';
import {setChats, updateUnreadCounter} from '../../store/chatsSlice';
import {getSocket} from '../../utilities/socket';

const useChats = () => {
  const [isLoading, setIsLoading] = useState(false);
  const {user} = useAppSelector(state => state.user);
  const dispatch = useAppDispatch();
  // const socket = getSocket();

  // const socket = io('https://api.theplatemate.ca/', {
  //   query: {userId: user?._id},
  // });

  // useEffect(() => {
  //   socket?.on('connect', () => {
  //     console.log('Socket connected');
  //   });
  //   return () => {
  //     socket?.disconnect();
  //   };
  // }, []);

  // useEffect(() => {
  //   socket?.on('newChat', chat => {
  //     console.log('New chat recieved:', chat);
  //     fetchChats();
  //   });

  //   socket?.on('newMessage', msg => {
  //     console.log('New message recieved:', msg._id);
  //     fetchChats();
  //   });
  // }, [socket]);

  const fetchChats = useCallback(async () => {
    try {
      setIsLoading(true);
      const resp: AxiosResponse<IQueryResponse<IChat>> =
        await ChatServices.GetChats(20);
      if (resp.status == 200) {
        // SET GLOBAL COUNTER OF UNREAD MESSAGES
        const {results} = resp.data;
        let unreadCount = 0;
        results.forEach(chat => {
          if (chat.unReadMsgCount && chat.lastMessage.sender !== user._id) {
            unreadCount += chat.unReadMsgCount;
          }
        });
        dispatch(setChats(resp.data));
        dispatch(updateUnreadCounter({counter: unreadCount}));
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }, [user._id]);

  return {
    // socket,
    fetchChats,
    isLoading,
  };
};

export default useChats;
