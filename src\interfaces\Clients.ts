import {IMealWithFoods} from './IMeal';
import {IGoal, INutrients, IUser} from './IUser.';

export interface IClientUser {
  _id: string;
  name: string;
  email: string;
  password: string;
  code: string;
  verificationCodeExpiresAt: number;
  referralCode: string;
  photos: string[];
  coach: ICoachUser;
  userType: string;
  questions: SignupQuestionare[];
  createdAt: string;
  updatedAt: string;
  image: string;
  __v: number;
}

export interface IClientsResponse {
  data: IUser[];
  count: number;
}

export interface ICoachUser {
  _id: string;
  name: string;
  email: string;
  password: string;
  experience: string;
  about: string;
  photo: string;
  code: string;
  verificationCodeExpiresAt: number;
  referralCode: string;
  userType: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface SignupQuestionare {
  _id: string;
  prompt: string;
  value: string[];
  step: number;
  questionType: string;
  user: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IClientDetails {
  client: IClientUser;
  usedData?: INutrients;
  mealData: IMealWithFoods[];
  goal?: IGoal;
}
