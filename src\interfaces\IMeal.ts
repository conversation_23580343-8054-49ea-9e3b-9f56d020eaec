import {IFood} from './IFoods';

export interface IMeal {
  mealTitle: 'Breakfast' | 'Lunch' | 'Dinner' | 'Snacks';
  mealFoods: IFood[];
  client: string;
  createdAt: string;
  updatedAt: string;
  _id: string;
}

export interface MealPayload extends Omit<IMeal, 'mealFoods'> {
  mealFoods: IFoodMeal[];
}

export interface IFoodMeal {
  foodId: string;
  serving: string;
}

export interface IMealDetails extends Omit<IMeal, 'mealFoods'> {
  mealFoods: Array<IFood>;
  review: null | IMealReview;
}

export interface IMealWithFoods extends Omit<IMeal, 'mealFoods'> {
  mealFoods: IFood[];
}

export interface IFoodItem {
  foodId: string;
  serving: string;
  food: IFood;
}

export interface IMealReview {
  _id: string;
  text?: string;
  isReacted?: boolean;
  coach: string;
  client: string;
  meal: string;
  createdAt?: string;
  updatedAt?: string;
  __v?: number;
}

export interface IMealPost {
  _id: string;
  mealTitle: string;
  mealFoods: IFoodItem[];

  client: {
    _id: string;
    name: string;
    image: string;
  };

  coach: string;
  createdAt: string;
  updatedAt: string;

  review: null | IMealReview;
  totalNutrients: {
    totalCalories: number;
    totalProteins: number;
    totalCarbs: number;
    totalFats: number;
  };
}
