import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Image, Platform, Text} from 'react-native';
import PNGIcons from '../../assets/pngIcons';
import {Fonts, theme} from '../../utilities/theme';
import {Home, Clients, Chats, Profile} from '../../screens/homeflow';
import {IDocument} from '../../interfaces/IDocument';
import {IUser} from '../../interfaces/IUser.';
import {View} from 'react-native';
import {useAppSelector} from '../../store';
import {useTranslation} from 'react-i18next';

export type BottomStackParamList = {
  Home: undefined;
  Clients: undefined;
  Chats: undefined;
  Profile: undefined;
  Notifications: undefined;
  Archive: undefined;
  Language2: {isClient?: boolean};
  PreviousPassword: undefined;
  EmailVerification: undefined;
  ResetPassword: {
    previousPassword: string;
    reset_channel: 'forgotPassword' | 'changePassword';
  };
  OTPVerification: undefined;
  PasswordFeedback: undefined;
  TermsAndCondition: undefined;
  About: undefined;
  PersonalInfo: undefined;
  ViewSubscription: {isProfile: boolean};
  AddNewCard: {isProfile: boolean};
  Document: undefined;
  AddDocument: undefined;
  EditDocument: {document: IDocument};
  HelpCenter: undefined;
  ShareBarcode: undefined;
  ClientDetail: {details: IUser};
  ClientHistory: undefined;
  ChatDetails: {recipient?: IUser; chatId?: string};
  EditGoals: undefined;
  WeightProgress: undefined;
  AddClient: undefined;
  DocumentDetail: {document: IDocument};
  Subscription2: {isProfile: boolean};
};
const BottomStack = createBottomTabNavigator<BottomStackParamList>();

const BottomNavigation = () => {
  const unreadCounter = useAppSelector(state => state.chats.unreadCounter);
  const {t} = useTranslation();

  const renderTabBarIcon = (iconSource: any, color: string) => {
    return (
      <Image
        source={iconSource}
        style={{
          width: 22,
          height: 22,
          tintColor: color,
        }}
      />
    );
  };

  const renderTabBarLabel = (
    label: string,
    focused: boolean,
    color: string,
  ) => {
    return (
      <View style={{alignItems: 'center'}}>
        <Text
          style={{
            fontSize: 10,
            fontFamily: Fonts.regular,
            color: color,
            marginTop: -10,
          }}>
          {label}
        </Text>
        <View
          style={{
            width: 4,
            height: 4,
            borderRadius: 2,
            backgroundColor: focused ? theme.lightColors?.white : 'transparent',
            marginTop: 2,
          }}
        />
      </View>
    );
  };
  return (
    <BottomStack.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({color, size, focused}) => {
          let iconSource;
          if (route.name === 'Home') {
            iconSource = PNGIcons.HomeIcon;
          } else if (route.name === 'Clients') {
            iconSource = PNGIcons.SolarUser;
          } else if (route.name === 'Chats') {
            iconSource = PNGIcons.Message;
          } else if (route.name === 'Profile') {
            iconSource = PNGIcons.SolarUser2;
          }
          return renderTabBarIcon(iconSource, color);
        },
        tabBarLabel: ({focused, color}) => {
          let label;
          if (route.name === 'Home') {
            label = t('Home');
          } else if (route.name === 'Clients') {
            label = t('Clients');
          } else if (route.name === 'Chats') {
            label = t('Message');
          } else if (route.name === 'Profile') {
            label = t('Profile');
          }
          return renderTabBarLabel(label, focused, color);
        },
        tabBarActiveTintColor: theme.lightColors?.white,
        tabBarInactiveTintColor: `${theme.lightColors?.white}90`,
        headerShown: false,
        tabBarStyle: {
          backgroundColor: theme.lightColors?.primary,
          height: Platform.OS === 'ios' ? 85 : 70,
          paddingBottom: 19,
        },

        tabBarHideOnKeyboard: true,
      })}>
      <BottomStack.Screen
        name="Home"
        component={Home}
        options={{
          tabBarLabel: ({focused, color}) =>
            renderTabBarLabel(t('Home'), focused, color),
        }}
      />
      <BottomStack.Screen
        name="Clients"
        component={Clients}
        options={{
          tabBarLabel: ({focused, color}) =>
            renderTabBarLabel(t('Clients'), focused, color),
        }}
      />

      <BottomStack.Screen
        name="Chats"
        component={Chats}
        options={{
          tabBarIcon: ({focused, color}) => (
            <View style={{position: 'relative'}}>
              {unreadCounter ? (
                <View
                  style={{
                    position: 'absolute',
                    top: -5,
                    right: -7,
                    backgroundColor: 'red',
                    paddingHorizontal: 5,
                    paddingVertical: 2,
                    borderRadius: 100,
                    zIndex: 10,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                  <Text
                    style={{
                      fontSize: 8,
                      color: 'white',
                    }}>
                    {unreadCounter}
                  </Text>
                </View>
              ) : null}

              <Image
                resizeMode="contain"
                source={PNGIcons.Message}
                style={[
                  {width: 22, height: 22},
                  {
                    tintColor: color,
                  },
                ]}
              />
            </View>
          ),

          tabBarLabel: ({focused, color}) =>
            renderTabBarLabel(t('Message'), focused, color),
        }}
      />
      <BottomStack.Screen
        name="Profile"
        component={Profile}
        options={{
          tabBarLabel: ({focused, color}) =>
            renderTabBarLabel(t('Profile'), focused, color),
        }}
      />
    </BottomStack.Navigator>
  );
};

export default BottomNavigation;
