import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {
  Notifications,
  Archive,
  TermsAndCondition,
  About,
} from '../../screens/homeflow';
import BottomNavigation, {
  BottomStackParamList,
} from '../BottomNavigation/BottomNavigation';
import {StyleSheet, TouchableOpacity} from 'react-native';
import {BackArrow} from '../../assets/svgIcons';
import {Fonts, theme} from '../../utilities/theme';
import {
  AddNewCard,
  EmailVerification,
  Language,
  OTPVerification,
  PasswordFeedback,
  PaymentFeedback,
  PreviousPassword,
  ResetPassword,
  Subscription,
} from '../../screens/authflow';
import PersonalInfo from '../../screens/homeflow/PersonalInformation';
import Document from '../../screens/homeflow/Document';
import AddDocument from '../../screens/homeflow/AddDocument';
import EditDocument from '../../screens/homeflow/EditDocument';
import ViewSubscription from '../../screens/homeflow/ViewSubscription';
import HelpCenter from '../../screens/homeflow/HelpCenter';
import ShareBarcode from '../../screens/homeflow/ShareBarcode';
import ClientDetail from '../../screens/homeflow/ClientDetail';
import ClientHistory from '../../screens/homeflow/ClientHistory';
import ChatDetails from '../../screens/homeflow/ChatDetails';
import EditGoals from '../../screens/homeflow/EditGoals';
import WeightProgress from '../../screens/homeflow/WeightProgress';
import AddClient from '../../screens/homeflow/AddClient';
import DocumentDetail from '../../screens/homeflow/DocumentDetail';
import {IClientUser} from '../../interfaces/Clients';
import {IDocument} from '../../interfaces/IDocument';
import {IGoal, IUser} from '../../interfaces/IUser.';
import {useTranslation} from 'react-i18next';

export type HomeStackParamList = {
  BottomTabNavigator: BottomStackParamList;
  Notifications: undefined;
  Archive: undefined;
  Language2: {isClient?: boolean};
  PreviousPassword: undefined;
  EmailVerification: undefined;
  ResetPassword: {
    previousPassword: string;
    reset_channel: 'forgotPassword' | 'changePassword';
  };
  OTPVerification: undefined;
  PasswordFeedback: undefined;
  TermsAndCondition: undefined;
  About: undefined;
  PersonalInfo: undefined;
  ViewSubscription: {isProfile: boolean};
  AddNewCard: {isProfile: boolean};
  PaymentFeedback: undefined;
  Document: {isClient?: boolean};
  AddDocument: undefined;
  EditDocument: {document: IDocument};
  HelpCenter: undefined;
  ShareBarcode: undefined;
  ClientDetail: {details: IUser};
  ChatDetails: {recipient?: IUser; chatId?: string};
  ClientHistory: {clientId: string};
  EditGoals: {clientId: string; currentGoal: IGoal};
  WeightProgress: undefined;
  AddClient: undefined;
  DocumentDetail: {document: IDocument};
  Subscription2: {isProfile: boolean};
};

const HomeStack = createNativeStackNavigator<HomeStackParamList>();

const HomeStackNavigator = () => {
  const {t} = useTranslation();

  return (
    <HomeStack.Navigator
      screenOptions={({navigation}) => ({
        headerShown: false,
        headerShadowVisible: false,
        headerTitleAlign: 'center',
        headerLeft: () => (
          <TouchableOpacity
            style={{width: 30}}
            onPress={() => navigation.goBack()}
            hitSlop={styles.hitSlop}>
            <BackArrow stroke={theme.lightColors?.secondary} />
          </TouchableOpacity>
        ),
        headerTitleStyle: styles.headerTitleStyles,
        headerStyle: {backgroundColor: theme.lightColors?.background},
      })}>
      <HomeStack.Screen
        name="BottomTabNavigator"
        component={BottomNavigation}
      />
      <HomeStack.Screen
        name="Notifications"
        component={Notifications}
        options={{
          headerShown: true,
          headerTitle: t('Notifications'),
        }}
      />
      <HomeStack.Screen
        name="Archive"
        component={Archive}
        options={{
          headerShown: true,
          headerTitle: t('Archive'),
        }}
      />
      <HomeStack.Screen
        name="Language2"
        component={Language}
        options={{
          headerShown: true,
          headerTitle: t('Select Language'),
        }}
      />
      <HomeStack.Screen
        name="PreviousPassword"
        component={PreviousPassword}
        options={{
          headerShown: true,
          headerTitle: t('Change Password'),
        }}
      />
      <HomeStack.Screen
        name="EmailVerification"
        component={EmailVerification}
        options={{headerShown: true, headerTitle: t('Forgot Password')}}
      />
      <HomeStack.Screen
        name="ResetPassword"
        component={ResetPassword}
        options={{headerShown: true, headerTitle: t('Reset Password')}}
      />
      <HomeStack.Screen
        name="OTPVerification"
        component={OTPVerification}
        options={{headerShown: true, headerTitle: t('Forgot Password')}}
      />
      <HomeStack.Screen name="PasswordFeedback" component={PasswordFeedback} />
      <HomeStack.Screen
        name="TermsAndCondition"
        component={TermsAndCondition}
        options={{headerShown: true, headerTitle: t('Terms & Conditions')}}
      />
      <HomeStack.Screen
        name="About"
        component={About}
        options={{headerShown: true, headerTitle: t('About')}}
      />
      <HomeStack.Screen
        name="PersonalInfo"
        component={PersonalInfo}
        options={{headerShown: true, headerTitle: t('Personal Info')}}
      />

      <HomeStack.Screen
        name="ViewSubscription"
        component={ViewSubscription}
        options={{headerShown: true, headerTitle: t('Subscription')}}
      />
      <HomeStack.Screen
        name="Subscription2"
        component={Subscription}
        options={{headerShown: true, headerTitle: t('Subscription')}}
      />
      <HomeStack.Screen
        name="AddNewCard"
        component={AddNewCard}
        options={{headerShown: true, headerTitle: t('Payment')}}
      />
      <HomeStack.Screen
        name="Document"
        component={Document}
        options={{headerShown: true, headerTitle: t('Document')}}
      />
      <HomeStack.Screen
        name="AddDocument"
        component={AddDocument}
        options={{headerShown: true, headerTitle: t('Add New Document')}}
      />

      <HomeStack.Screen
        name="EditDocument"
        component={EditDocument}
        options={{headerShown: true, headerTitle: t('Edit Document')}}
      />
      <HomeStack.Screen
        name="HelpCenter"
        component={HelpCenter}
        options={{headerShown: true, headerTitle: t('Help Center')}}
      />

      <HomeStack.Screen
        name="ShareBarcode"
        component={ShareBarcode}
        options={{headerShown: true, headerTitle: t('My QR')}}
      />

      <HomeStack.Screen
        name="ClientDetail"
        component={ClientDetail}
        options={{headerShown: true, headerTitle: ''}}
      />

      <HomeStack.Screen
        name="ClientHistory"
        component={ClientHistory}
        options={{headerShown: true, headerTitle: t('History')}}
      />

      <HomeStack.Screen
        name="ChatDetails"
        component={ChatDetails}
        options={{headerShown: false}}
      />

      <HomeStack.Screen
        name="EditGoals"
        component={EditGoals}
        options={{headerShown: true, headerTitle: t('Daily Goals')}}
      />

      <HomeStack.Screen
        name="WeightProgress"
        component={WeightProgress}
        options={{headerShown: true, headerTitle: t('Weight Progress')}}
      />

      <HomeStack.Screen
        name="AddClient"
        component={AddClient}
        options={{headerShown: true, headerTitle: t('Add Client')}}
      />

      <HomeStack.Screen
        name="DocumentDetail"
        component={DocumentDetail}
        options={{headerShown: true, headerTitle: t('Details')}}
      />
      <HomeStack.Screen name="PaymentFeedback" component={PaymentFeedback} />
    </HomeStack.Navigator>
  );
};

export default HomeStackNavigator;
const styles = StyleSheet.create({
  hitSlop: {
    left: 15,
    right: 15,
    bottom: 15,
    top: 15,
  },
  headerTitleStyles: {
    fontSize: 16,
    color: theme.lightColors?.black,
    fontFamily: Fonts.semiBold,
  },
});
