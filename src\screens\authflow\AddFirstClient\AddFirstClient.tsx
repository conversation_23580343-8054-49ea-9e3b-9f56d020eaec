import React, {useEffect, useLayoutEffect, useState} from 'react';
import {color, Text} from '@rneui/base';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import View from '../../../components/View';
import {Button, FormInput, ModalSuccess} from '../../../components';
import {Fonts, theme} from '../../../utilities/theme';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {setUser} from '../../../store/userSlice';
import AuthServices from '../../../services/AuthServices';
import {useAppDispatch} from '../../../store';
import {useTranslation} from 'react-i18next';
import InviteServices from '../../../services/InviteServices';
import Toast from 'react-native-toast-message';
import {TouchableOpacity} from 'react-native-gesture-handler';

type Props = NativeStackScreenProps<AuthStackParamList, 'AddFirstClient'>;

const Services = new AuthServices();

const AddFirstClient: React.FC<Props> = ({navigation}) => {
  const [isLoading, setLoading] = useState(false);
  const [isButtonLoading, setButtonLoading] = useState(false);

  const [isModalVisible, setModalVisible] = useState(false);
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  const validationSchema = Yup.object().shape({
    email: Yup.string()
      .email(t('Invalid email'))
      .required(t('Email is required')),
    firstName: Yup.string().required(t('Name is required')),
  });

  const formik = useFormik({
    initialValues: {
      firstName: '',
      email: '',
    },
    validationSchema: validationSchema,
    onSubmit: async values => {
      try {
        setButtonLoading(true);
        const resp = await InviteServices.SendInvites({
          name: values.firstName,
          clientEmail: values.email.toLowerCase(),
        });
        if (resp.status == 201) {
          setButtonLoading(false);
          setLoading(true);
          handleCloseModal();
        }
      } catch (error: any) {
        console.log('error in sending invite', error?.response?.data);
        setButtonLoading(false);
        let errorMessage = error?.response?.data?.message || '';
        if (
          error?.response.data.message === 'A duplicate entry already exists.'
        ) {
          errorMessage = 'This client is already linked to a different coach';
        }

        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: errorMessage,
        });
      }
    },
    validateOnBlur: true,
  });

  const handleCloseModal = async () => {
    const response = await Services.CoachGetMe();
    setModalVisible(true);
    setTimeout(() => {
      setLoading(false);
      setModalVisible(false);
      dispatch(setUser({id: response.data._id, ...response.data}));
    }, 2500);
  };

  const handleSkip = async () => {
    const response = await Services.CoachGetMe();
    dispatch(setUser({id: response.data._id, ...response.data}));
  };
  useLayoutEffect(() => {
    // Skip Button
    navigation.setOptions({
      headerRight: () => (
        <TouchableOpacity onPress={handleSkip}>
          <Text style={{color: theme.lightColors?.grey4}}>{t('Skip')}</Text>
        </TouchableOpacity>
      ),
    });
  }, [navigation, t]);

  return (
    <>
      <SafeAreaView style={styles.screenWrapper}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={styles.flex}>
          <ScrollView
            contentContainerStyle={styles.container}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}>
            <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
              <View style={styles.innerContainer}>
                {/* TITLE */}
                <Text style={styles.title}>
                  {t("Let's add your first client")}
                </Text>
                <Text style={styles.subtitle}>
                  {t('Add your first client through email')}
                </Text>
                {/* FIRST NAME */}
                <FormInput
                  label={t('Name')}
                  value={formik.values.firstName}
                  onChangeText={formik.handleChange('firstName')}
                  onBlur={formik.handleBlur('firstName')}
                  errorMessage={
                    formik.touched.firstName
                      ? t(formik.errors.firstName as string)
                      : undefined
                  }
                />
                {/* EMAIL */}
                <FormInput
                  label={t('Email')}
                  value={formik.values.email}
                  onChangeText={formik.handleChange('email')}
                  onBlur={formik.handleBlur('email')}
                  keyboardType="email-address"
                  errorMessage={
                    formik.touched.email
                      ? t(formik.errors.email as string)
                      : undefined
                  }
                />

                <ModalSuccess
                  isModalVisible={isModalVisible}
                  setModalVisible={setModalVisible}
                  paragraph={t(
                    'Your first client is added successfully and also invite sent via email.',
                  )}
                />
              </View>
            </TouchableWithoutFeedback>

            <Button
              title={t('Send Invite')}
              onPress={formik.handleSubmit}
              containerStyle={{marginTop: 70}}
              disabled={isButtonLoading || !formik.isValid || !formik.dirty}
              loading={isButtonLoading}
            />
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </>
  );
};

export default AddFirstClient;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  flex: {
    flex: 1,
  },
  container: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  innerContainer: {
    backgroundColor: theme.lightColors?.background,
    flex: 1,
  },
  title: {
    fontSize: 24,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.bold,
    // paddingTop: 24,
    marginBottom: 14,
    textAlign: 'left',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 30,
    color: `${theme.lightColors?.secondary}60`,
    lineHeight: 21,
    fontFamily: Fonts.regular,
  },
});
