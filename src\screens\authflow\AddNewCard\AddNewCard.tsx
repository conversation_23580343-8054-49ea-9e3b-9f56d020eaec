import React from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Fonts, theme} from '../../../utilities/theme';
import {Button, FormInput} from '../../../components';
import {Divider} from '@rneui/base';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {useTranslation} from 'react-i18next';

const validationSchema = Yup.object().shape({
  promoCode: Yup.string().required('PromoCode is required'),
  cardName: Yup.string().required('Card name is required'),
  cardNumber: Yup.string().required('Card number is required'),
  cvv: Yup.string().required('CVV is required'),
  expiry: Yup.string().required('Expiry date is required'),
});

type Props = NativeStackScreenProps<AuthStackParamList, 'AuthAddNewCard'>;

const AddNewCard: React.FC<Props> = ({navigation, route}) => {
  // const isProfile = route?.params?.isProfile;
  const {t} = useTranslation();

  const onSubmit = (values: any) => {
    console.log('Form submitted:', values);
    navigation.navigate('PaymentFeedback');
    // if (isProfile) {
    //   // navigation.pop(2);
    //   navigation.goBack();
    // } else {
    //   navigation.navigate('PaymentFeedback');
    // }
  };

  const formik = useFormik({
    initialValues: {
      promoCode: '',
      cardName: '',
      cardNumber: '',
      cvv: '',
      expiry: '',
    },
    validationSchema: validationSchema,
    onSubmit: onSubmit,
  });

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.flex}>
        <ScrollView
          contentContainerStyle={styles.scrollViewContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View style={styles.container}>
              <View>
                <Text style={styles.title}>
                  {t('Please enter your details to pay')}.
                </Text>
                <FormInput
                  label={t('Add Promo Code')}
                  labelStyle={{marginTop: 16}}
                  value={formik.values.promoCode}
                  onChangeText={formik.handleChange('promoCode')}
                  onBlur={formik.handleBlur('promoCode')}
                  errorMessage={
                    formik.touched.promoCode
                      ? t(formik.errors.promoCode as string)
                      : undefined
                  }
                />
                <Divider color={theme.lightColors?.grey2} />
                <FormInput
                  label={t('Card Name')}
                  labelStyle={{marginTop: 16}}
                  value={formik.values.cardName}
                  onChangeText={formik.handleChange('cardName')}
                  onBlur={formik.handleBlur('cardName')}
                  errorMessage={
                    formik.touched.cardName
                      ? t(formik.errors.cardName as string)
                      : undefined
                  }
                />
                <FormInput
                  label={t('Card Number')}
                  labelStyle={{marginTop: 0}}
                  value={formik.values.cardNumber}
                  onChangeText={formik.handleChange('cardNumber')}
                  onBlur={formik.handleBlur('cardNumber')}
                  keyboardType="number-pad"
                  errorMessage={
                    formik.touched.cardNumber
                      ? t(formik.errors.cardNumber as string)
                      : undefined
                  }
                />
                <View style={styles.goalInputs}>
                  <View style={{flex: 1}}>
                    <FormInput
                      label={t('CVV')}
                      value={formik.values.cvv}
                      onChangeText={formik.handleChange('cvv')}
                      onBlur={formik.handleBlur('cvv')}
                      keyboardType="number-pad"
                      errorMessage={
                        formik.touched.cvv
                          ? t(formik.errors.cvv as string)
                          : undefined
                      }
                    />
                  </View>
                  <View style={{flex: 1, paddingLeft: 15}}>
                    <FormInput
                      label={t('Expiry')}
                      value={formik.values.expiry}
                      onChangeText={formik.handleChange('expiry')}
                      onBlur={formik.handleBlur('expiry')}
                      keyboardType="number-pad"
                      errorMessage={
                        formik.touched.expiry
                          ? t(formik.errors.expiry as string)
                          : undefined
                      }
                    />
                  </View>
                </View>
              </View>

              <Button
                title={t('Continue')}
                containerStyle={styles.continueButton}
                onPress={formik.handleSubmit}
              />
            </View>
          </TouchableWithoutFeedback>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default AddNewCard;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingBottom: 32,
  },
  flex: {
    flex: 1,
  },
  container: {
    flexGrow: 1,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
    marginTop: 35,
  },
  scrollViewContent: {
    flexGrow: 1,
  },
  title: {
    fontSize: 14,
    fontFamily: Fonts.medium,
    color: `${theme.lightColors?.secondary}90`,
    marginBottom: 16,
  },
  continueButton: {
    marginTop: 90,
  },
  goalInputs: {flexDirection: 'row', alignItems: 'center'},
});
