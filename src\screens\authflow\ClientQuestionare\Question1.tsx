import {
  ActivityIndicator,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {memo, useMemo, useState} from 'react';
import PNGIcons from '../../../assets/pngIcons';
import {Fonts, sizes, theme} from '../../../utilities/theme';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import QuestionareHeader from './QuestionareHeader';
import ClientServices from '../../../services/ClientServices';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {AxiosError} from 'axios';
import {useTranslation} from 'react-i18next';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import moment from 'moment';
import ScrollPicker from 'react-native-wheel-scrollview-picker';
import AgePicker from '../../../components/AgePicker/AgePicker';

type Props = NativeStackScreenProps<AuthStackParamList, 'Question1'>;
const MemoizedAgePicker = memo(AgePicker);
const Question1: React.FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const {t} = useTranslation();
  const [isDatePickerVisible, setIsDatePickerVisible] = useState(false);

  const MIN_VALUE = 15;
  const MAX_VALUE = 100;

  const data = useMemo(
    () =>
      Array.from({length: MAX_VALUE - MIN_VALUE + 1}, (_, i) =>
        (i + MIN_VALUE).toString(),
      ),
    [MIN_VALUE, MAX_VALUE],
  );
  const [currentVal, setCurrentVal] = useState<Date>(
    moment().subtract(15, 'years').toDate(),
  );

  const prompt = t('What is your current age?');

  async function addQuestionClient() {
    setIsLoading(true);
    const userData = await AsyncStorage.getItem('userData');

    if (userData) {
      try {
        const resp = await ClientServices.UpdateClient(
          JSON.parse(userData).id,
          {
            step: 1,
            dob: moment(currentVal).format('YYYY-MM-DD').toString(),
          },
        );

        // SUCCESS
        if (resp.status === 200) {
          setIsLoading(false);
          navigation.navigate('Question2');
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as AxiosError;
        console.log('error ----:', err.response);
      }
    }
  }

  return (
    <View style={styles.container}>
      <SafeAreaView />
      <QuestionareHeader progress={0.142} />
      <View style={styles.promptContainer}>
        <Text style={styles.headingText}>{prompt}</Text>

        <MemoizedAgePicker
          data={data}
          setCurrentVal={(val: string) => {
            const birthDate = moment(val, 'YYYY').toDate();
            setCurrentVal(birthDate);
          }}
        />
        {/* DATE PICKER */}
        <Text style={styles.descText}>
          {t(
            'We use this information to calculate and provide you with daily personalized recommendations.',
          )}
        </Text>
      </View>

      {/* NEXT ARROW */}
      <TouchableOpacity
        style={styles.buttonStyle}
        disabled={!currentVal || isLoading}
        onPress={addQuestionClient}>
        {!isLoading ? (
          <Image
            source={!currentVal ? PNGIcons.NextDisabled : PNGIcons.Next}
            style={styles.nextIcon}
          />
        ) : (
          <ActivityIndicator
            size={'small'}
            style={styles.activityIndicator}
            color={theme.lightColors?.primary}
          />
        )}
      </TouchableOpacity>

      <DateTimePickerModal
        isVisible={isDatePickerVisible}
        date={currentVal}
        maximumDate={new Date()}
        mode="date"
        onConfirm={date => {
          setCurrentVal(date);
          setIsDatePickerVisible(false);
        }}
        onCancel={() => setIsDatePickerVisible(false)}
      />
    </View>
  );
};

export default Question1;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.background,
    paddingTop: sizes.paddingTop,
  },
  headingText: {
    color: theme.lightColors?.secondary,
    marginTop: sizes.adjustedHeight,
    alignSelf: 'center',
    fontSize: 20,
    fontFamily: Fonts.semiBold,
  },
  descText: {
    color: '#2C2C2E99',
    textAlign: 'center',
    fontSize: 14,
    fontFamily: Fonts.regular,
  },
  nextIcon: {
    color: theme.lightColors?.black,
    height: 60,
    width: 60,
  },
  activityIndicator: {
    height: 60,
    width: 60,
    borderRadius: 30,
    backgroundColor: theme.lightColors?.grey1,
  },
  datePicker: {
    backgroundColor: `${theme.lightColors?.grey3}66`,
    borderColor: theme.lightColors?.grey3,
    borderWidth: 1,
    borderRadius: 10,
    padding: 18,
  },
  promptContainer: {justifyContent: 'space-between', flex: 1},
  dateStyle: {color: theme.lightColors?.grey4, textAlign: 'center'},
  buttonStyle: {marginVertical: 35, alignSelf: 'flex-end'},
});
