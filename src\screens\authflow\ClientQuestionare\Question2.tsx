import {
  ActivityIndicator,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import PNGIcons from '../../../assets/pngIcons';
import {Fonts, sizes, theme} from '../../../utilities/theme';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import QuestionareHeader from './QuestionareHeader';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ClientServices from '../../../services/ClientServices';
import {AxiosError} from 'axios';
import {useTranslation} from 'react-i18next';
import OnboardingCard from '../../../components/OnboardingCard/OnboardingCard';

type Props = NativeStackScreenProps<AuthStackParamList, 'Question2'>;
const Question2: React.FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedGender, setSelectedGender] = useState<
    'male' | 'female' | 'skip' | undefined
  >();
  const {t} = useTranslation();

  const prompt = t('How do you define your gender?');

  async function addQuestionClient() {
    setIsLoading(true);
    const userData = await AsyncStorage.getItem('userData');
    if (userData) {
      try {
        const resp = await ClientServices.UpdateClient(
          JSON.parse(userData).id,
          {
            step: 2,
            gender: selectedGender,
          },
        );

        // SUCCESS
        if (resp.status === 200) {
          setIsLoading(false);
          navigation.navigate('Question3');
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as AxiosError;
        console.log(err.response);
      }
    }
  }

  return (
    <View style={[styles.container]}>
      <SafeAreaView />
      <QuestionareHeader progress={0.284} />
      <View style={styles.innerContainer}>
        <Text style={[styles.headingText, styles.headingStyle]}>{prompt}</Text>
        <View>
          <OnboardingCard
            label={t('Male')}
            isActive={selectedGender === 'male'}
            onPressItem={() => setSelectedGender('male')}
            isMargin
          />
          <OnboardingCard
            label={t('Female')}
            isActive={selectedGender === 'female'}
            onPressItem={() => setSelectedGender('female')}
            isMargin
          />
          <OnboardingCard
            label={t('Prefer to skip, thanks!')}
            isActive={selectedGender === 'skip'}
            onPressItem={() => setSelectedGender('skip')}
            isSkip
          />
        </View>
        <Text style={styles.infoText}>
          {t(
            'We use this information to calculate and provide you with daily personalized recommendations.',
          )}
        </Text>
      </View>
      <TouchableOpacity
        style={styles.buttonStyle}
        disabled={!selectedGender || isLoading}
        onPress={addQuestionClient}>
        {!isLoading ? (
          <Image
            source={!selectedGender ? PNGIcons.NextDisabled : PNGIcons.Next}
            style={styles.nextIcon}
          />
        ) : (
          <ActivityIndicator
            size={'small'}
            style={styles.activityIndicator}
            color={theme.lightColors?.primary}
          />
        )}
      </TouchableOpacity>
    </View>
  );
};

export default Question2;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.background,
    paddingTop: sizes.paddingTop,
  },
  headingText: {
    marginTop: sizes.adjustedHeight,
    alignSelf: 'center',
    fontSize: 20,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
  },
  infoText: {
    color: '#2C2C2E99',
    textAlign: 'center',
    fontSize: 14,
    fontFamily: Fonts.regular,
  },
  genderText: {
    color: theme.lightColors?.black,
    textAlign: 'center',
    fontFamily: Fonts.medium,
    fontSize: 14,
  },
  genderContainer: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    paddingVertical: 25,
  },
  selectedGender: {
    borderColor: theme.lightColors?.primary,
  },
  nextIcon: {
    color: theme.lightColors?.black,
    height: 60,
    width: 60,
  },
  activityIndicator: {
    height: 60,
    width: 60,
    borderRadius: 30,
    backgroundColor: theme.lightColors?.grey1,
  },
  innerContainer: {justifyContent: 'space-between', flex: 1},
  headingStyle: {width: '70%', textAlign: 'center'},
  buttonStyle: {
    marginVertical: 35,
    alignSelf: 'flex-end',
  },
});
