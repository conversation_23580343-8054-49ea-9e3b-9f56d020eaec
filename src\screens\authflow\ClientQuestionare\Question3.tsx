import {
  ActivityIndicator,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Fonts, sizes, theme} from '../../../utilities/theme';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import QuestionareHeader from './QuestionareHeader';
import {FormInput} from '../../../components';
import PNGIcons from '../../../assets/pngIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ClientServices from '../../../services/ClientServices';
import {AxiosError} from 'axios';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<AuthStackParamList, 'Question3'>;

const Question3: React.FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [unit, setUnit] = useState<'ft/in' | 'meter'>('ft/in');
  const [feetValue, setFeetValue] = useState('');
  const [inchValue, setInchValue] = useState('');
  const [rightUnitText, setRightUnitText] = useState("''");
  const [leftUnitText, setLeftUnitText] = useState("'");
  const {t} = useTranslation();

  const prompt = t('What is your current height?');
  useEffect(() => {
    setLeftUnitText(unit === 'ft/in' ? "'" : 'm');
    setRightUnitText(unit === 'ft/in' ? "''" : 'cm');
  }, [unit]);

  async function addQuestionClient() {
    setIsLoading(true);
    const userData = await AsyncStorage.getItem('userData');
    const clientHieght = feetValue + '.' + inchValue + ' ' + unit;

    if (userData) {
      try {
        const resp = await ClientServices.UpdateClient(
          JSON.parse(userData).id,
          {
            step: 3,
            height: clientHieght,
          },
        );

        // SUCCESS
        if (resp.status === 200) {
          setIsLoading(false);
          navigation.navigate('Question4');
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as AxiosError;
        console.log(err.response);
      }
    }
  }

  return (
    <View style={styles.container}>
      <SafeAreaView />
      <QuestionareHeader progress={0.426} />
      <View style={{justifyContent: 'space-between', flex: 1}}>
        <Text style={[styles.headingText, styles.headingStyle]}>{prompt}</Text>
        <View>
          <View style={styles.goalInputs}>
            <View style={{flex: 1}}>
              <FormInput
                value={feetValue}
                label=""
                placeholder={`0`}
                placeholderTextColor={theme.lightColors?.placeholderColor}
                onChangeText={v => setFeetValue(v)}
                textinputStyles={{paddingVertical: 15, textAlign: 'center'}}
                keyboardType="numeric"
                returnKeyType="done"
                inputContainerStyle={styles.inputContainer}
                unitText={leftUnitText}
              />
            </View>
            <View style={{flex: 1, paddingLeft: 15}}>
              <FormInput
                value={inchValue}
                label=""
                placeholder={`0`}
                placeholderTextColor={theme.lightColors?.placeholderColor}
                onChangeText={v => setInchValue(v)}
                textinputStyles={styles.inchInput}
                keyboardType="numeric"
                returnKeyType="done"
                inputContainerStyle={styles.inputContainer}
                unitText={rightUnitText}
              />
            </View>
          </View>
          <View style={styles.rowContainer}>
            <TouchableOpacity
              style={[
                styles.unitContainer,
                {
                  backgroundColor:
                    unit === 'ft/in'
                      ? theme.lightColors?.primary
                      : theme.lightColors?.grey1,
                },
              ]}
              onPress={() => setUnit('ft/in')}>
              <Text
                style={{
                  color:
                    unit === 'ft/in' ? theme.lightColors?.white : '#969696',
                }}>
                ft/in
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.unitContainer,
                {
                  backgroundColor:
                    unit === 'meter'
                      ? theme.lightColors?.primary
                      : theme.lightColors?.grey1,
                },
              ]}
              onPress={() => setUnit('meter')}>
              <Text
                style={{
                  color:
                    unit === 'meter' ? theme.lightColors?.white : '#969696',
                }}>
                {t('Meter')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.infoText}>
          {t(
            'We use this information to calculate and provide you with daily personalized recommendations.',
          )}
        </Text>
      </View>
      <TouchableOpacity
        style={styles.buttonStyle}
        disabled={(!inchValue && !feetValue) || isLoading}
        onPress={addQuestionClient}>
        {!isLoading ? (
          <Image
            source={
              !inchValue && !feetValue ? PNGIcons.NextDisabled : PNGIcons.Next
            }
            style={styles.nextIcon}
          />
        ) : (
          <ActivityIndicator
            size={'small'}
            style={styles.activityIndicator}
            color={theme.lightColors?.primary}
          />
        )}
      </TouchableOpacity>
    </View>
  );
};

export default Question3;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.background,
    paddingTop: sizes.paddingTop,
  },
  headingText: {
    marginTop: sizes.adjustedHeight,
    alignSelf: 'center',
    fontSize: 20,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
  },
  infoText: {
    color: '#2C2C2E99',
    textAlign: 'center',
    fontSize: 14,
    fontFamily: Fonts.regular,
  },
  goalInputs: {
    flexDirection: 'row',
    alignItems: 'center',
    color: theme.lightColors?.black,
  },
  unitContainer: {
    marginRight: 12,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
  },
  nextIcon: {
    color: theme.lightColors?.black,
    height: 60,
    width: 60,
  },
  activityIndicator: {
    height: 60,
    width: 60,
    borderRadius: 30,
    backgroundColor: theme.lightColors?.grey1,
  },
  inchInput: {paddingVertical: 15, textAlign: 'center'},
  buttonStyle: {marginVertical: 35, alignSelf: 'flex-end'},
  rowContainer: {flexDirection: 'row', justifyContent: 'center'},
  headingStyle: {width: '70%', textAlign: 'center'},
  inputContainer: {
    backgroundColor: theme.lightColors?.white,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 10,
  },
});
