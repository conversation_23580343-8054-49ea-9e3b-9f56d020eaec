import {
  ActivityIndicator,
  Image,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {Fonts, sizes, theme} from '../../../utilities/theme';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import QuestionareHeader from './QuestionareHeader';
import PNGIcons from '../../../assets/pngIcons';
import ImagePicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import {AxiosError} from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ClientServices from '../../../services/ClientServices';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import {TextButton} from '../../../components';

type Props = NativeStackScreenProps<AuthStackParamList, 'Question3'>;

const Question5: React.FC<Props> = ({navigation}) => {
  const [isLoading, setLoading] = useState(false);
  const [sideImage, setSideImage] = useState<ImageOrVideo>();
  const [backImage, setBackImage] = useState<ImageOrVideo>();
  const [frontImage, setFrontImage] = useState<ImageOrVideo>();
  const {t} = useTranslation();

  const prompt = t(
    'Please upload your current photos to compare your weight progress',
  );

  const handlePickeImage = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 300,
        height: 400,
        cropping: false,
        compressImageQuality: 0.25,
        mediaType: 'photo',
      });
      return image;
    } catch (error) {
      console.log('Error selecting image', error);
    }
  };
  const handleSideImage = async () => {
    const imageUri = await handlePickeImage();
    if (imageUri) setSideImage(imageUri);
  };
  const handleBackImage = async () => {
    const imageUri = await handlePickeImage();
    if (imageUri) setBackImage(imageUri);
  };
  const handleFrontImage = async () => {
    const imageUri = await handlePickeImage();
    if (imageUri) setFrontImage(imageUri);
  };

  // UPLOAD IMAGES
  const uploadImage = async () => {
    try {
      const formData = new FormData();
      formData.append('side', {
        uri: Platform.OS == 'ios' ? sideImage?.sourceURL : sideImage?.path,
        type: sideImage?.mime,
        name: `side`,
      });
      formData.append('front', {
        uri: Platform.OS == 'ios' ? frontImage?.sourceURL : frontImage?.path,
        type: frontImage?.mime,
        name: `front`,
      });
      formData.append('back', {
        uri: Platform.OS == 'ios' ? backImage?.sourceURL : backImage?.path,
        type: backImage?.mime,
        name: `back`,
      });

      const resp = await ClientServices.UploadMultiImage(formData);
      if (resp.status == 201) {
        return resp.data;
      }
    } catch (error) {
      console.log('----------- upload images:', error);
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t("Couldn't upload the images."),
      });
    }
  };

  // ADD QEUSTION
  async function addQuestionClient() {
    const userData = await AsyncStorage.getItem('userData');

    if (userData && frontImage && sideImage && backImage) {
      setLoading(true);
      try {
        const uploadedImagesArr = await uploadImage();

        // SUCCESS
        navigation.navigate('Question6');
      } catch (error) {
        const err = error as AxiosError;
        console.log(err.response?.data);
      } finally {
        setLoading(false);
      }
    } else {
      navigation.navigate('Question6');
    }
  }

  const isButtonDisabled = () => {
    if (isLoading) {
      return true;
    }
    if (!frontImage && !sideImage && !backImage) {
      return true;
    }
    if (frontImage && sideImage && backImage) {
      return false;
    }
    return true;
  };

  return (
    <View style={styles.container}>
      <SafeAreaView />

      <QuestionareHeader
        progress={0.71}
        showSkip
        onPressSkip={() => navigation.navigate('Question6')}
      />
      <ScrollView
        contentContainerStyle={{flex: 1}}
        showsVerticalScrollIndicator={false}>
        <View style={{justifyContent: 'space-between', flex: 1}}>
          <Text
            style={[styles.headingText, {width: '90%', textAlign: 'center'}]}>
            {prompt}
            {/* <Text style={styles.textOptional}> ({t('optional')})</Text> */}
          </Text>
          <View>
            <View style={styles.uploadContainer}>
              <View style={{width: '46.5%'}}>
                <Text style={styles.sideText}>{t('Front')}</Text>
                <TouchableOpacity
                  style={frontImage ? {} : styles.uploadImageContainer}
                  onPress={handleFrontImage}>
                  <Image
                    source={
                      frontImage ? {uri: frontImage.path} : PNGIcons.UploadImage
                    }
                    style={frontImage ? styles.sideImage : styles.uploadImage}
                  />
                </TouchableOpacity>
              </View>
              <View style={{width: '46.5%'}}>
                <Text style={styles.sideText}>{t('Back')}</Text>
                <TouchableOpacity
                  style={backImage ? {} : styles.uploadImageContainer}
                  onPress={handleBackImage}>
                  <Image
                    source={
                      backImage ? {uri: backImage.path} : PNGIcons.UploadImage
                    }
                    style={backImage ? styles.sideImage : styles.uploadImage}
                  />
                </TouchableOpacity>
              </View>
            </View>
            <TouchableOpacity
              style={{width: '46.5%', marginTop: 10}}
              onPress={handleSideImage}>
              <Text style={styles.sideText}>{t('Side')}</Text>
              <View style={sideImage ? {} : styles.uploadImageContainer}>
                <Image
                  source={
                    sideImage ? {uri: sideImage.path} : PNGIcons.UploadImage
                  }
                  style={sideImage ? styles.sideImage : styles.uploadImage}
                />
              </View>
            </TouchableOpacity>
          </View>
          <Text style={styles.infoText}>
            {t(
              'We use this information to calculate and provide you with daily personalized recommendations.',
            )}
          </Text>
        </View>

        {/* SKIP BUTTON */}

        {/* NEXT ARROW */}

        <TouchableOpacity
          style={{marginVertical: 35, alignSelf: 'flex-end'}}
          // disabled={false}
          disabled={isButtonDisabled()}
          onPress={addQuestionClient}>
          {!isLoading ? (
            <Image
              source={
                isButtonDisabled() ? PNGIcons.NextDisabled : PNGIcons.Next
              }
              style={styles.nextIcon}
            />
          ) : (
            <ActivityIndicator
              size={'small'}
              style={styles.activityIndicator}
              color={theme.lightColors?.primary}
            />
          )}
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default Question5;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.background,
    paddingTop: sizes.paddingTop,
  },
  headingText: {
    marginTop: sizes.adjustedHeight,
    alignSelf: 'center',
    fontSize: 20,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
  },
  infoText: {
    color: '#2C2C2E99',
    textAlign: 'center',
    fontSize: 14,
    fontFamily: Fonts.regular,
  },
  unitContainer: {
    marginRight: 12,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
  },
  uploadImageContainer: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    height: 140,
    width: 140,
    borderRadius: 13,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sideText: {
    fontFamily: Fonts.medium,
    fontSize: 14,
    marginBottom: 6,
    color: theme.lightColors?.secondary,
  },
  uploadContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  uploadImage: {height: 44, width: 41},
  sideImage: {
    height: 140,
    width: 140,
    borderRadius: 13,
  },
  nextIcon: {
    color: theme.lightColors?.black,
    height: 60,
    width: 60,
  },
  activityIndicator: {
    height: 60,
    width: 60,
    borderRadius: 30,
    backgroundColor: theme.lightColors?.grey1,
  },
  textOptional: {fontFamily: Fonts.light, fontSize: 14},
  buttonTitle: {textAlign: 'center', color: theme.lightColors?.primary},
  buttonContainer: {
    marginVertical: 35,
    alignSelf: 'flex-end',
  },
});
