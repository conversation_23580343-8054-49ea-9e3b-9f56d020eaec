import {
  Dimensions,
  Image,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import images from '../../../assets/images';
import {Button, FormInput} from '../../../components';
import {Button as TextButton} from '@rneui/themed';
import * as Yup from 'yup';
import {useFormik} from 'formik';
import {ScrollView} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
type Props = NativeStackScreenProps<AuthStackParamList, 'Login'>;
import {setUser} from '../../../store/userSlice';
import {useDispatch} from 'react-redux';
import AuthServices from '../../../services/AuthServices';
import {AxiosError} from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
import useApiHandler from '../../../utilities/useApiHandler';

const validationSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string().required('Password is required'),
});
const Services = new AuthServices();

const Login: React.FC<Props> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  const [hidePassword, setHidePassword] = React.useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();
  const handleLogin = async () => {
    setIsLoading(true);

    try {
      const resp = await Services.SignIn({
        email: formik.values.email.toLowerCase(),
        password: formik.values.password,
      });

      if (resp.status == 200) {
        AsyncStorage.setItem('access_token', resp.data.access_token as string);
        dispatch(setUser({id: resp.data?._id, ...resp.data}));
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('AUTH > LOGIN --> ', err.response?.data);
      // INVALID EMAIL OR PASSWORD
      if (err.response?.status === 401) {
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t('Invalid email or password.'),
        });
        return;
      }
      // AXIOS ERROR HANDLER
      handleAxiosErrors(err);
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },
    validationSchema: validationSchema,
    validateOnMount: true,
    onSubmit: handleLogin,
  });

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={{flex: 1}}>
        <ScrollView
          contentContainerStyle={{flexGrow: 1}}
          showsVerticalScrollIndicator={false}>
          <View style={styles.container}>
            {/* LOGO */}
            <Image source={images.logo} style={styles.logoContainer} />
            {/* TITLE */}
            <Text style={styles.title}>{t('Login')}</Text>
            <Text style={styles.paragraph}>{t('Welcome back !')}</Text>
            {/* EMAIL */}
            <FormInput
              label={t('Email')}
              value={formik.values.email}
              onChangeText={formik.handleChange('email')}
              onBlur={formik.handleBlur('email')}
              errorMessage={
                formik.touched.email
                  ? t(formik?.errors?.email as string)
                  : undefined
              }
              keyboardType="email-address"
            />

            {/* PASSWORD */}
            <FormInput
              label={t('Password')}
              isPassword={true}
              value={formik.values.password}
              onChangeText={formik.handleChange('password')}
              onBlur={formik.handleBlur('password')}
              errorMessage={
                formik.touched.password
                  ? t(formik?.errors?.password as string)
                  : undefined
              }
              secureTextEntry={hidePassword}
              onRightIconPress={() => setHidePassword(!hidePassword)}
            />
            {/* CHECK-BOX */}
            {/* <CheckBox
              title={t('Remember me')}
              checked={check}
              onPress={() => setCheck(!check)}
              containerStyle={styles.containerStyle}
              textStyle={styles.textStyle}
              size={16}
              checkedIcon={<SquareChecked />}
              uncheckedIcon={<Square />}
            /> */}

            {/* LOGIN BUTTON */}
            <Button
              title={t('Login')}
              onPress={formik.handleSubmit}
              containerStyle={styles.loginButton}
              disabled={!formik.isValid || isLoading}
              loading={isLoading}
            />

            {/* FORGOT PASSWORD BUTTON */}
            <View style={{marginTop: 42}} />
            <TextButton
              title={t('Forgot Password?')}
              type="clear"
              titleStyle={styles.forgotPasswordText}
              hitSlop={styles.hitSlop}
              onPress={() => navigation.navigate('EmailVerification')}
            />
            {/*  */}
            <View style={[styles.footerContainer]}>
              <Text style={styles.footerText}>
                {t(`Don't have an account?`)}
              </Text>
              <TextButton
                title={t('Register Now')}
                type="clear"
                titleStyle={styles.titleStyle}
                onPress={() => navigation.navigate('SelectRole')}
              />
            </View>
          </View>
          {/* FOOTER */}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default Login;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  container: {
    paddingHorizontal: 24,
  },
  logoContainer: {
    width: 140,
    height: 140,
    alignSelf: 'center',
    marginTop: Platform.OS === 'ios' ? 24 : 56,
  },
  title: {
    fontSize: 24,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.bold,
    paddingTop: 28,
    textAlign: 'left',
    lineHeight: 26,
  },
  paragraph: {
    fontSize: 14,
    color: '#********',
    fontFamily: Fonts.regular,
    paddingBottom: 12,
    paddingLeft: 8,
  },

  containerStyle: {
    marginLeft: 0,
    marginTop: 0,
    paddingVertical: 0,
    paddingLeft: 0,
  },
  textStyle: {
    fontSize: 12,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.light,
    marginLeft: 5,
    fontWeight: '400',
  },
  loginButton: {
    marginBottom: 10,
    marginHorizontal: 4,
  },
  forgotPasswordText: {
    fontSize: 16,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.semiBold,
  },
  hitSlop: {
    left: 10,
    right: 10,
    bottom: 10,
    top: 10,
  },
  footerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginTop: Dimensions.get('screen').height * 0.07,
  },
  footerText: {
    fontSize: 14,
    fontWeight: '400',
    color: `${theme.lightColors?.black}80`,
    fontFamily: Fonts.regular,
  },
  titleStyle: {
    fontSize: 16,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.semiBold,
  },
});
