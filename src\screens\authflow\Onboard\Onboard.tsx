import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  ScrollView,
  Dimensions,
  SafeAreaView,
  StyleSheet,
  Animated,
  Text,
  BackHandler,
} from 'react-native';
import {SLIDES} from '../../../constants/Slider';
import {Button} from '../../../components';
import {Fonts, theme} from '../../../utilities/theme';
import {Button as TextButton} from '@rneui/themed';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useAppDispatch} from '../../../store';
import {setFirstLaunch} from '../../../store/userSlice';
import {useTranslation} from 'react-i18next';

type Props = NativeStackScreenProps<AuthStackParamList, 'Onboard'>;

const Onboard: React.FC<Props> = ({navigation}) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const windowWidth = Dimensions.get('window').width;
  const scrollViewRef = useRef(null);
  const scrollX = new Animated.Value(0);
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  // Handle hardware back button to navigate to Language screen
  useEffect(() => {
    const backAction = () => {
      navigation.navigate('Language');
      return true; // Prevent default back action
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, [navigation]);

  const onBackButtonPress = async () => {
    let newIndex = selectedIndex + 1;
    if (newIndex == 3) {
      navigation.replace('SelectRole');
      await AsyncStorage.setItem('appLaunched', 'true');
      dispatch(setFirstLaunch(true));
      return;
    }

    setSelectedIndex(newIndex);
    const scrollPosition = newIndex * windowWidth;
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        x: scrollPosition,
        animated: true,
      });
    }
  };

  const handleLoginPress = async () => {
    navigation.replace('Login', {isClient: true});
    await AsyncStorage.setItem('appLaunched', 'true');
    dispatch(setFirstLaunch(true));
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.mainContainer}>
        {/* Moving Content Area - Images and Text */}
        <View style={styles.movingContentContainer}>
          <ScrollView
            // contentContainerStyle={{flex: 1}}
            ref={scrollViewRef}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={event => {
              const selectedIndex = Math.floor(
                event.nativeEvent.contentOffset.x / Math.floor(windowWidth),
              );
              setSelectedIndex(selectedIndex);
            }}
            onScroll={Animated.event(
              [{nativeEvent: {contentOffset: {x: scrollX}}}],
              {useNativeDriver: false},
            )}
            scrollEventThrottle={16}>
            {SLIDES.map((item, index) => {
              const inputRange = [
                (index - 1) * windowWidth,
                index * windowWidth,
                (index + 1) * windowWidth,
              ];

              const scale = scrollX.interpolate({
                inputRange,
                outputRange: [0.8, 1, 0.8],
                extrapolate: 'clamp',
              });

              const opacity = scrollX.interpolate({
                inputRange,
                outputRange: [0.3, 1, 0.3],
                extrapolate: 'clamp',
              });

              const translateX = scrollX.interpolate({
                inputRange,
                outputRange: [50, 0, -50],
                extrapolate: 'clamp',
              });

              return (
                <View
                  key={item.id}
                  style={[styles.slideContainer, {width: windowWidth}]}>
                  {/* ANIMATED IMAGE */}
                  <Animated.Image
                    resizeMode="contain"
                    source={item.image}
                    style={[
                      styles.imageContainer,
                      {
                        transform: [{scale}, {translateX}],
                        opacity,
                      },
                    ]}
                  />

                  {/* ANIMATED TITLE */}
                  <Animated.Text
                    style={[
                      styles.title,
                      {
                        transform: [{scale}, {translateX}],
                        opacity,
                      },
                    ]}>
                    {t(item.title)}
                  </Animated.Text>

                  {/* ANIMATED PARAGRAPH */}
                  <Animated.Text
                    style={[
                      styles.paragraph,
                      {
                        transform: [{scale}, {translateX}],
                        opacity,
                      },
                    ]}>
                    {t(item.paragraph)}
                  </Animated.Text>
                </View>
              );
            })}
          </ScrollView>
        </View>

        {/* Static Content Area - Dots, Button, Footer */}
        <View style={styles.staticContentContainer}>
          {/* DOTS CONTAINER */}
          <View style={styles.dotContainer}>
            {SLIDES.map((_, index) => {
              const inputRange = [
                (index - 1) * windowWidth,
                index * windowWidth,
                (index + 1) * windowWidth,
              ];

              const dotWidth = scrollX.interpolate({
                inputRange,
                outputRange: [8, 23, 8],
                extrapolate: 'clamp',
              });

              const dotOpacity = scrollX.interpolate({
                inputRange,
                outputRange: [0.3, 1, 0.3],
                extrapolate: 'clamp',
              });

              return (
                <Animated.View
                  key={index}
                  style={[
                    styles.dot,
                    {
                      width: dotWidth,
                      opacity: dotOpacity,
                      backgroundColor:
                        index === selectedIndex
                          ? theme.lightColors?.primary
                          : theme.lightColors?.grey2,
                    },
                  ]}
                />
              );
            })}
          </View>

          {/* CONTINUE BUTTON */}
          <View style={styles.buttonContainer}>
            <Button
              title={t('Get Started')}
              containerStyle={styles.buttonStyle}
              onPress={onBackButtonPress}
            />
          </View>

          {/* FOOTER */}
          <View style={styles.footerContainer}>
            <Text style={styles.footerText}>
              {t('Already have an account?')}
            </Text>
            <TextButton
              title={t('Log in')}
              type="clear"
              titleStyle={styles.titleStyle}
              onPress={handleLoginPress}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Onboard;

const {height, width} = Dimensions.get('window');

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  mainContainer: {
    flex: 1,
  },
  movingContentContainer: {
    flex: 1,
    minHeight: height * 0.65, // Ensure consistent height for moving content
  },
  slideContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 40,
    paddingHorizontal: 20,
  },
  imageContainer: {
    width: height * 0.32,
    height: height * 0.32,
    marginBottom: 20,
  },
  title: {
    fontSize: 20,
    textAlign: 'center',
    lineHeight: 27,
    marginBottom: 18,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
    paddingHorizontal: 20,
  },
  paragraph: {
    fontSize: 16,
    paddingHorizontal: 27,
    textAlign: 'center',
    color: '#2C2C2E80',
    lineHeight: 21,
    fontFamily: Fonts.regular,
  },
  staticContentContainer: {
    flex: 1,
    paddingBottom: 50,

    backgroundColor: theme.lightColors?.background,
    alignItems: 'center',
  },
  dotContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    height: 50, // Fixed height for consistent positioning
  },
  dot: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 3,
    backgroundColor: theme.lightColors?.grey2,
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: 24,
    marginBottom: 20,
  },
  buttonStyle: {
    marginHorizontal: 0,
  },
  footerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerText: {
    fontSize: 14,
    fontWeight: '400',
    color: `${theme.lightColors?.black}70`,
    fontFamily: Fonts.regular,
  },
  titleStyle: {
    fontSize: 16,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.semiBold,
  },
});
