import {SafeAreaView, StyleSheet, View} from 'react-native';
import React from 'react';
import {theme} from '../../../utilities/theme';
import {Feedback} from '../../../components';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import {useAppSelector} from '../../../store';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<AuthStackParamList, 'PasswordFeedback'>;

const PasswordFeedback: React.FC<Props> = ({navigation}) => {
  const user = useAppSelector(state => state.user.user);
  const {t} = useTranslation();

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        <Feedback
          title={t('Password Changed')}
          paragraph={t(
            'The password has been successfully recovered, you can log in back with a new password',
          )}
          onPress={() => (user._id ? navigation.pop(5) : navigation.goBack())}
        />
      </View>
    </SafeAreaView>
  );
};

export default PasswordFeedback;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
  },
});
