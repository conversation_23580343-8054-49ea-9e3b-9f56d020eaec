import {SafeAreaView, StyleSheet, View} from 'react-native';
import React from 'react';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import {Feedback} from '../../../components';
import {theme} from '../../../utilities/theme';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<AuthStackParamList, 'PaymentFeedback'>;

const PaymentFeedback: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        <Feedback
          title={t('Payment Successful')}
          paragraph={t('Your subscription is confirmed.')}
          onPress={() => navigation.pop(3)}
        />
      </View>
    </SafeAreaView>
  );
};

export default PaymentFeedback;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
  },
});
