import React, {useState} from 'react';
import {Text} from '@rneui/base';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import View from '../../../components/View';
import {Avatar, Button, FormInput, ModalSuccess} from '../../../components';
import {Fonts, theme} from '../../../utilities/theme';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {AxiosError} from 'axios';
import AuthServices from '../../../services/AuthServices';
import ImagePicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useTranslation} from 'react-i18next';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import useApiHandler from '../../../utilities/useApiHandler';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

type Props = NativeStackScreenProps<AuthStackParamList, 'ProfileSetup'>;
const Services = new AuthServices();
const validationSchema = Yup.object().shape({
  dob: Yup.string().required('Date of birth is required'),
  experience: Yup.string().required('Experience is required'),
  about: Yup.string().required('About is required'),
});

const ProfileSetup: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  const [isDatePickerVisible, setIsDatePickerVisible] = useState(false);

  const [isModalVisible, setModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState<ImageOrVideo>();
  const [isLoading, setLoading] = useState(false);

  const handleImageSelect = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 300,
        height: 400,
        cropping: false,
        mediaType: 'photo',
      });
      setSelectedImage(image);
    } catch (error) {
      console.log('Error selecting image', error);
    }
  };

  const handleDataUpdate = async () => {
    try {
      const userDataString = await AsyncStorage.getItem('userData');
      const userData = JSON.parse(userDataString || '');
      const payload = {
        dob: moment(formik.values.dob).format('YYYY-MM-DD'),
        experience: formik.values.experience,
        about: formik.values.about,
      };

      setLoading(true);
      // const imageUrl = await uploadImage();
      const resp = await Services.CoachUpdate(payload, userData.id);
      if (resp.status == 200) {
        setLoading(false);
        setModalVisible(true);
        setTimeout(() => {
          setModalVisible(false);
          navigation.navigate('AddFirstClient');
        }, 3000);
      }
    } catch (error) {
      setLoading(false);
      const err = error as AxiosError;
      handleAxiosErrors(err);

      console.log('Error updating data', err.response?.data);
    }
  };

  const formik = useFormik({
    initialValues: {
      dob: '',
      experience: '',
      about: '',
    },
    validationSchema: validationSchema,
    onSubmit: handleDataUpdate,
  });

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.container}
        enableAutomaticScroll={true}
        enableOnAndroid={true}
        extraScrollHeight={100} // Increased from 20 to 100
        keyboardShouldPersistTaps="handled"
        extraHeight={Platform.OS === 'ios' ? 200 : 150} // Added extra height
        resetScrollToCoords={{x: 0, y: 0}} // Added reset scroll coords
        scrollEventThrottle={16} // Added for smoother scrolling
        style={styles.flex}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.innerContainer}>
            {/* TITLE */}
            <Text style={styles.title}>{t('Profile Setup')}</Text>
            <Text style={styles.subtitle}>
              {t('Please enter your details to setup your profile.')}
            </Text>
            {/* PROFILE AVATAR */}
            <Avatar
              uri={selectedImage?.path ? selectedImage.path : ''}
              onPress={handleImageSelect}
              showPickImage
            />
            {/* DOB */}
            <Text
              style={{
                color: theme.lightColors?.black,
                fontFamily: Fonts.light,
                marginBottom: 8,
              }}>
              {t('DOB')}
            </Text>
            <TouchableOpacity
              style={[
                styles.datePicker,
                {
                  borderColor: formik.values.dob
                    ? theme.lightColors?.primary
                    : theme.lightColors?.grey1,
                },
              ]}
              onPress={() => setIsDatePickerVisible(true)}>
              <Text
                style={{
                  color: theme.lightColors?.black,
                  fontFamily: Fonts.medium,
                }}>
                {!formik.values?.dob
                  ? t('Pick Date')
                  : moment(formik.values?.dob).format('MMMM Do, YYYY')}
              </Text>
            </TouchableOpacity>
            <Text style={styles.dateStyles}>
              {formik.touched.dob ? t(formik.errors.dob as string) : undefined}
            </Text>

            {/* EXPERIENCE */}
            <FormInput
              label={t('Experience (years)')}
              value={formik.values.experience}
              onChangeText={formik.handleChange('experience')}
              onBlur={formik.handleBlur('experience')}
              errorMessage={
                formik.touched.experience
                  ? t(formik.errors.experience as string)
                  : undefined
              }
              keyboardType="number-pad"
            />
            {/* ABOUT */}
            <FormInput
              label={t('About')}
              value={formik.values.about}
              errorMessage={
                formik.touched.about
                  ? t(formik.errors.about as string)
                  : undefined
              }
              onChangeText={formik.handleChange('about')}
              onBlur={formik.handleBlur('about')}
              multiline
              textAlignVertical="top"
              scrollEnabled={false} // Disable internal scrolling
              blurOnSubmit={false} // Prevent dismissing keyboard on submit
              returnKeyType="default" // Allow line breaks
              textinputStyles={{
                height: 100,
              }}
            />

            {/* SIGN UP BUTTON */}
            <Button
              title={t('Done')}
              onPress={formik.handleSubmit}
              containerStyle={{marginTop: 20}} // Added bottom margin
              loading={isLoading}
              disabled={isLoading || !formik.isValid}
            />
            <ModalSuccess
              isModalVisible={isModalVisible}
              setModalVisible={setModalVisible}
              paragraph={t('Your profile created successfully.')}
            />
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAwareScrollView>
      <DateTimePickerModal
        isVisible={isDatePickerVisible}
        date={
          formik.values.dob ? moment(formik.values.dob).toDate() : new Date()
        }
        maximumDate={new Date()}
        mode="date"
        onConfirm={date => {
          setIsDatePickerVisible(false);
          formik.setFieldValue('dob', moment(date).format('YYYY-MM-DD'));
        }}
        onCancel={() => setIsDatePickerVisible(false)}
      />
    </SafeAreaView>
  );
};

export default ProfileSetup;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  flex: {
    flex: 1,
  },
  container: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  innerContainer: {
    backgroundColor: theme.lightColors?.background,
    flex: 1,
  },

  title: {
    fontSize: 24,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.bold,
    paddingTop: 24,
    marginBottom: 14,
    textAlign: 'left',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 30,
    color: `${theme.lightColors?.secondary}60`,
    lineHeight: 21,
    fontFamily: Fonts.regular,
  },
  datePicker: {
    backgroundColor: theme.lightColors?.background,

    borderWidth: 1,
    borderRadius: 10,
    marginBottom: 6,
    padding: 14,
  },
  dateStyles: {
    color: theme.lightColors?.error,
    fontFamily: Fonts.semiBold,
    fontSize: 12,
    marginBottom: 14,
    marginLeft: 8,
  },
});
