import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {Button, FormInput} from '../../../components';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import AuthServices from '../../../services/AuthServices';
import {AxiosError} from 'axios';
import Toast from 'react-native-toast-message';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import {useTranslation} from 'react-i18next';
import useApiHandler from '../../../utilities/useApiHandler';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

type Props = NativeStackScreenProps<AuthStackParamList, 'ResetPassword'> &
  NativeStackScreenProps<HomeStackParamList, 'ResetPassword'>;

// Define validation schema using Yup
const validationSchema = Yup.object().shape({
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), undefined], 'Passwords must match')
    .required('Confirm Password is required'),
});
const Services = new AuthServices();

const ResetPassword: React.FC<Props> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  const [isLoading, setLoading] = useState(false);
  const {email, otp} = route.params;
  const {previousPassword, reset_channel} = route.params;

  const handleChangePassword = async () => {
    setLoading(true);
    const _forgotPasswordPayload = {
      password: formik.values.password,
      email,
      otp,
    };
    const _changePasswordPayload = {
      oldPassword: previousPassword,
      newPassword: formik.values.password,
    };

    try {
      const resp =
        reset_channel === 'changePassword'
          ? await Services.ChangePassword(_changePasswordPayload)
          : await Services.ResetPassword(_forgotPasswordPayload);

      // SUCCESS CHECK OF PASSWORD
      if (resp.status === 200) {
        Toast.show({
          type: 'success',
          text1: t('Success'),
          text2: t('Password has been updated.'),
        });
        navigation.replace('PasswordFeedback');
      }
    } catch (error) {
      const err = error as AxiosError;
      const errResp = err.response?.data as Partial<{
        message?: string;
        statusCode?: number;
      }>;
      console.log(err.response?.data, '-- RESET PASSWORD --');
      // PASSWORD INCORRECT
      if (
        errResp.statusCode === 400 &&
        errResp.message === 'Old password is incorrect'
      ) {
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t('Invalid password has been entered.'),
        });
        return;
      }
      // AXIOS ERROR HANDLER
      handleAxiosErrors(err);
    }
    setLoading(false);
  };
  // Initialize useFormik hook
  const formik = useFormik({
    initialValues: {
      password: '',
      confirmPassword: '',
    },
    validationSchema: validationSchema,
    onSubmit: handleChangePassword,
  });
  const [hidePassword, setHidePassword] = React.useState(true);
  const [hidePassword2, setHidePassword2] = React.useState(true);

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <KeyboardAwareScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        extraScrollHeight={20}
        enableOnAndroid={true}>
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.container}>
            <View>
              {/* TITLE */}
              <Text style={styles.title}>
                {t('Create a new password that is easy to remember.')}
              </Text>
              {/* PASSWORD */}
              <FormInput
                label={t('Password')}
                isPassword={true}
                value={formik.values.password}
                onChangeText={formik.handleChange('password')}
                errorMessage={
                  formik.touched.password
                    ? t(formik.errors.password as string)
                    : undefined
                }
                secureTextEntry={hidePassword}
                onRightIconPress={() => setHidePassword(!hidePassword)}
                onBlur={formik.handleBlur('password')}
              />
              {/* CONFIRM PASSWORD */}
              <FormInput
                label={t('Confirm Password')}
                isPassword={true}
                value={formik.values.confirmPassword}
                onChangeText={formik.handleChange('confirmPassword')}
                errorMessage={
                  formik.touched.confirmPassword
                    ? t(formik.errors.confirmPassword as string)
                    : undefined
                }
                secureTextEntry={hidePassword2}
                onRightIconPress={() => setHidePassword2(!hidePassword2)}
                onBlur={formik.handleBlur('confirmPassword')}
              />
            </View>

            {/* CONTINUE BUTTON */}
            <Button
              title={t('Continue')}
              onPress={formik.handleSubmit}
              disabled={!formik.isValid || isLoading || !formik.dirty}
              loading={isLoading}
              containerStyle={{marginBottom: 10, marginHorizontal: 4}}
            />
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default ResetPassword;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'space-between',
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
    justifyContent: 'space-between',
    paddingBottom: Platform.OS === 'android' ? 40 : 20,
  },
  title: {
    fontSize: 14,
    color: `${theme.lightColors?.secondary}60`,
    fontFamily: Fonts.regular,
    fontWeight: '400',
    paddingBottom: 20,
  },
});
