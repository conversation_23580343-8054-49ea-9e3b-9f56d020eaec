import {
  Image,
  Platform,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamList} from '../../../navigation/AuthNavigation/AuthNavigation';
import {Fonts, theme} from '../../../utilities/theme';
import images from '../../../assets/images';
import {Button, FormInput} from '../../../components';
import {Button as TextButton} from '@rneui/themed';
import * as Yup from 'yup';
import {useFormik} from 'formik';
import AuthServices from '../../../services/AuthServices';
import {AxiosError} from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Toast from 'react-native-toast-message';
import {CLIENT_MEALS} from '../../../constants/Meals';
import MealServices from '../../../services/MealServices';
import {useTranslation} from 'react-i18next';
import useApiHandler from '../../../utilities/useApiHandler';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {Check, CheckFill, CheckStroke} from '../../../assets/svgIcons';

type Props = NativeStackScreenProps<AuthStackParamList, 'SignUp'>;

const Services = new AuthServices();
const SignUp: React.FC<Props> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  const [hideNewPassword, setHideNewPassword] = useState(true);
  const [hideConfirmPassword, setHideConfirmPassword] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  // Define the Yup validation schema
  const validationSchema = Yup.object().shape({
    name: Yup.string().required(t('Name is required')),
    email: Yup.string()
      .email(t('Invalid email'))
      .required(t('Email is required')),
    password: Yup.string()
      .min(8, t('Password must be at least 8 characters long'))
      .matches(
        /[A-Z]/,
        t('Password must contain at least one uppercase letter'),
      )
      .matches(
        /[a-z]/,
        t('Password must contain at least one lowercase letter'),
      )
      .matches(/[0-9]/, t('Password must contain at least one numeric digit'))
      .required(t('Password is required')),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref('password'), undefined], t('Passwords must match'))
      .required(t('Confirm password is required')),
    referralCode: Yup.string().optional(),
    termsAndCondition: Yup.boolean()
      .oneOf([true], t('You must accept the terms and conditions'))
      .required(t('You must accept the terms and conditions')),
  });
  const isClient = route.params?.isClient;

  const handleSignUp = async () => {
    const payload = {
      name: formik.values.name,
      email: formik.values.email.toLowerCase(),
      password: formik.values.confirmPassword,
    };

    try {
      setIsLoading(true);

      // CLIENT USER
      if (isClient) {
        const responseClient = await Services.ClientSignUp({
          ...payload,
          userType: 'client',
          referralCode: formik.values.referralCode,
        });

        // SUCCESS
        if (responseClient.status === 200) {
          await AsyncStorage.setItem(
            'access_token',
            responseClient?.data?.access_token,
          );
          createMealsWhileSignUp();
          await AsyncStorage.setItem(
            'userData',
            JSON.stringify(responseClient.data),
          );
        }
        navigation.navigate('Question1');
      }

      // COACH USER
      else {
        const resp = await Services.CoachSignUp({
          ...payload,
          userType: 'coach',
        });

        // SUCCESS
        if (resp.status == 200) {
          await AsyncStorage.setItem('access_token', resp?.data?.access_token);
          await AsyncStorage.setItem('userData', JSON.stringify(resp.data));
          // navigation.navigate('Subscription');
          navigation.replace('ProfileSetup');
        }
      }
    } catch (error) {
      const err = error as AxiosError;
      const errResp = err.response as Partial<{data: {message: string}}>;
      console.log('AUTH > SIGN UP --> ', err.response?.data);
      // EMAIL DUPLICATION
      if (errResp.data?.message == 'Email Already exist') {
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t('Email is already taken.'),
        });
        return;
      }
      // AXIOS ERROR HANDLER
      handleAxiosErrors(err);
    } finally {
      setIsLoading(false);
    }
  };

  // CREATE MEALS WHILE USER IS CERATING AN ACCOUNT
  async function createMealsWhileSignUp() {
    try {
      CLIENT_MEALS.forEach(async meal => {
        MealServices.CreateMeal({mealTitle: meal, mealFoods: []});
      });
    } catch (error) {
      const err = error as AxiosError;
      console.log('-- SIGN UP: CREATE MEAL API ---', err);
    }
  }

  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      referralCode: '',
      termsAndCondition: false,
    },
    validationSchema: validationSchema,
    onSubmit: handleSignUp,
  });

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.container} // Added this
        enableOnAndroid={true} // Added for Android
        extraScrollHeight={20} // Added extra space when keyboard appears
        keyboardShouldPersistTaps="handled" // Added to handle taps properly
      >
        {/* LOGO */}
        <Image source={images.logo} style={styles.logoContainer} />
        {/* TITLE */}
        <Text style={styles.title}>{t('Create Account')}</Text>
        <Text style={styles.paragraph}>
          {t('Please fill in all the required fields')}
        </Text>
        {/* NAME */}
        <FormInput
          label={t('Name')}
          value={formik.values.name}
          onChangeText={formik.handleChange('name')}
          errorMessage={
            formik.touched.name ? t(formik?.errors?.name as string) : undefined
          }
          onBlur={formik.handleBlur('name')}
        />
        {/* EMAIL */}
        <FormInput
          label={t('Email')}
          value={formik.values.email}
          onChangeText={formik.handleChange('email')}
          errorMessage={
            formik.touched.email
              ? t(formik?.errors?.email as string)
              : undefined
          }
          onBlur={formik.handleBlur('email')}
          keyboardType="email-address"
        />
        {/* PASSWORD */}
        <FormInput
          label={t('Password')}
          isPassword={true}
          value={formik.values.password}
          onChangeText={formik.handleChange('password')}
          onRightIconPress={() => setHideNewPassword(!hideNewPassword)}
          secureTextEntry={hideNewPassword}
          errorMessage={
            formik.touched.password
              ? t(formik?.errors?.password as string)
              : undefined
          }
          onBlur={formik.handleBlur('password')}
        />
        {/* CONFIRM PASSWORD */}
        <FormInput
          label={t('Confirm Password')}
          isPassword={true}
          value={formik.values.confirmPassword}
          onChangeText={formik.handleChange('confirmPassword')}
          onRightIconPress={() => setHideConfirmPassword(!hideConfirmPassword)}
          secureTextEntry={hideConfirmPassword}
          errorMessage={
            formik.touched.confirmPassword
              ? t(formik?.errors?.confirmPassword as string)
              : undefined
          }
          onBlur={formik.handleBlur('confirmPassword')}
        />

        {/* REFERRAL CODE */}
        {isClient ? (
          <FormInput
            label={t('Referral Code (optional)')}
            value={formik.values.referralCode}
            onChangeText={formik.handleChange('referralCode')}
            errorMessage={
              formik.touched.referralCode
                ? t(formik?.errors?.referralCode as string)
                : undefined
            }
            onBlur={formik.handleBlur('referralCode')}
          />
        ) : null}
        {/* Terms and */}
        <TouchableOpacity
          onPress={() =>
            formik.setFieldValue(
              'termsAndCondition',
              !formik.values.termsAndCondition,
            )
          }
          style={styles.rowContainer}>
          {formik.values.termsAndCondition ? (
            <CheckFill width={16} height={16} />
          ) : (
            <CheckStroke width={16} height={16} />
          )}
          <Text style={[styles.footerText, {marginLeft: 6}]}>
            {t('I accept all')}
          </Text>
          <Text style={styles.title2}>{t('Terms & Conditions')}</Text>
        </TouchableOpacity>

        {/* SIGN UP BUTTON */}
        <Button
          title={t('Create Account')}
          onPress={formik.handleSubmit}
          loading={isLoading}
          disabled={isLoading || !formik.isValid || !formik.dirty}
          containerStyle={{marginVertical: 10, marginHorizontal: 4}}
        />

        {/* FOOTER */}
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>{t('Already have an account?')}</Text>
          <TextButton
            title={t('Log in')}
            type="clear"
            titleStyle={styles.titleStyle}
            onPress={() => navigation.navigate('Login', {isClient: isClient})}
          />
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default SignUp;

const styles = StyleSheet.create({
  screenWrapper: {
    backgroundColor: theme.lightColors?.background,
  },
  container: {
    paddingHorizontal: 24,
    paddingBottom: 100,
  },
  innerContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  logoContainer: {
    width: 140,
    height: 140,
    alignSelf: 'center',
    marginTop: Platform.OS === 'ios' ? 24 : 56,
  },
  title: {
    fontSize: 24,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.bold,
    paddingTop: 28,
    textAlign: 'left',
    lineHeight: 26,
  },
  paragraph: {
    fontSize: 14,
    color: '#********',
    fontFamily: Fonts.regular,
    paddingBottom: 12,
    paddingLeft: 8,
  },
  textStyle: {
    fontSize: 12,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.light,
    marginLeft: 5,
    fontWeight: '400',
  },
  footerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  rowContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    alignSelf: 'center',
    marginBottom: 8,
  },
  footerText: {
    fontSize: 14,
    color: `${theme.lightColors?.black}80`,
    fontFamily: Fonts.regular,
  },
  title2: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
    marginLeft: 4,
  },
  titleStyle: {
    fontSize: 16,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.semiBold,
  },
});
