import {
  Image,
  LayoutAnimation,
  Platform,
  Pressable,
  Text,
  UIManager,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {Fonts} from '../../../utilities/theme';
import images from '../../../assets/images';
import PNGIcons from '../../../assets/pngIcons';
import ExerciseComponent from '../../../components/ExerciseComponent/ExerciseComponent';
import {styles} from './styles';
import GeneralModal from '../../../components/Modals/GeneralModal/GeneralModal';
import {Button, FormInput} from '../../../components';
import Modal from 'react-native-modal';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import {useTranslation} from 'react-i18next';
import {NewExercise} from './NewExercise';
import useActivity from '../../../hooks/models/useActivity';
import {useAppSelector} from '../../../store';
import useApiHandler from '../../../utilities/useApiHandler';
import {ExerciseLog} from '../../../interfaces/IActivity';
import {lightColors} from '@rneui/base';
import {IExercise} from '../../../interfaces/IExercise';
import {TouchableOpacity} from 'react-native-gesture-handler';
import CoachReviewCard from '../../../components/CoachReview/CoachReviewCard';

type Props = NativeStackScreenProps<ClientStackParamList, 'Activity'>;

if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
const Activity: React.FC<Props> = ({navigation}) => {
  const {user, dailyActivity} = useAppSelector(state => state.user);
  const [deleteExerciseModal, setDeleteExerciseModal] = useState(false);
  const [addExerciseVisible, setAddExerciseVisible] = useState(false);
  const [pushupModal, setPushupModal] = useState(false);
  const [aerobicModal, setAerobicModal] = useState(false);
  const [noSets, setNoSets] = useState('');
  const [reps, setNoReps] = useState('');
  const [minutes, setMinutes] = useState('');
  const {t} = useTranslation();
  const {addDailyActivityLog, deleteExerciseFromDailyLog} = useActivity();
  const {handleApiErrors} = useApiHandler();
  const [expandedReview, setExpandedReview] = useState(false);
  const [deletingExercise, setDeletingExercise] = useState<ExerciseLog>();
  const onExerciseCreated = async (exercise: IExercise) => {
    try {
      await addDailyActivityLog({
        clientId: user._id,
        date: new Date(),
        exercises: [
          {
            exercise: exercise._id,
            caloriesBurned: exercise.calories,
            duration: exercise.duration || 30,
          },
        ],
      });

      // console.log('Activity added', activity);
    } catch (error) {
      handleApiErrors(error);
    }
  };

  const onPressDeleteExercise = (exercise: ExerciseLog) => {
    if (typeof exercise.exercise === 'string') return;
    deleteExerciseFromDailyLog(user._id, exercise.exercise._id, new Date());
  };

  const renderExerciseLog = (exerciseLog: ExerciseLog) => {
    if (typeof exerciseLog.exercise === 'string') {
      console.error('Exercise should be object here');
      return null;
    }

    const {calories, duration, title} = exerciseLog.exercise;
    return (
      <ExerciseComponent
        key={exerciseLog.exercise._id}
        workout={title}
        time={duration || 0}
        calories={calories}
        onDeletePress={() => {
          setDeletingExercise(exerciseLog);
          setDeleteExerciseModal(true);
        }}
        onEditPress={() => setAerobicModal(true)}
      />
    );
  };

  return (
    <View style={styles.container}>
      <View>
        {/* <View style={styles.watchContainer}>
          <Image source={images.WatchImage} style={styles.watchImage} />
          <View style={{marginLeft: 18, flex: 1}}>
            <Text style={styles.watchHeading}>{t('Connect Wearables')}</Text>
            <Text style={styles.descriptionText}>
              {t('Auto calculate your steps and activity')}
            </Text>
            <TouchableOpacity
              style={styles.connectButton}
              activeOpacity={0.7}
              onPress={() => {
                console.log('Connect Now');
              }}>
              <Text style={styles.connectText}>{t('Connect Now')}</Text>
            </TouchableOpacity>
          </View>
        </View> */}
        <View style={styles.exerciseContainer}>
          <Text style={styles.semiBoldText}>{t('Today’s Exercise')}</Text>
          <Text style={styles.semiBoldText}>
            {dailyActivity?.totalCaloriesBurned || 0}{' '}
            <Text style={{fontFamily: Fonts.regular}}>{t('cal')}</Text>
          </Text>
        </View>
        {dailyActivity ? (
          dailyActivity?.exercises?.map(renderExerciseLog)
        ) : (
          <Text
            style={{
              alignSelf: 'center',
              marginTop: 40,
              color: lightColors.grey2,
            }}>
            {t('No Activity found for today')}
          </Text>
        )}
        <CoachReviewCard
          reviewText={t(
            'Lorem ipsum dolor sit amet consectetur. Commodo ac lectus fames ultricies nunc in ultrices. Nibh eu velit diam quis sollicitudin sagittis id et ultrices.',
          )}
        />
      </View>
      <Button
        title={t('+ Add Exercise')}
        containerStyle={{marginBottom: 30}}
        // onPress={() => navigation.navigate('AddExercise')}
        onPress={() => setAddExerciseVisible(true)}
      />
      <Modal
        isVisible={addExerciseVisible}
        animationIn={'slideInUp'}
        animationOut={'slideOutDown'}
        animationInTiming={500}
        animationOutTiming={500}
        onBackdropPress={() => setAddExerciseVisible(false)}
        backdropOpacity={0.3}
        avoidKeyboard
        style={{margin: 0, justifyContent: 'flex-end'}}>
        <NewExercise
          onPressAdd={() => setAddExerciseVisible(false)}
          onExerciseCreated={onExerciseCreated}
        />
      </Modal>
      <Modal
        isVisible={pushupModal}
        animationIn={'slideInUp'}
        animationOut={'slideOutDown'}
        animationInTiming={500}
        animationOutTiming={500}
        onBackdropPress={() => setPushupModal(false)}
        backdropOpacity={0.3}
        avoidKeyboard
        style={{margin: 0, justifyContent: 'flex-end'}}>
        <View style={styles.modalContainer}>
          <View>
            <Text style={styles.modalHeading}>{t('Push ups')}</Text>
            <View style={styles.divider} />
            <FormInput
              value={noSets}
              label={t('No. of sets')}
              onChangeText={v => setNoSets(v)}
              keyboardType="number-pad"
            />
            <FormInput
              value={reps}
              label={t('No. of reps')}
              onChangeText={v => setNoReps(v)}
              keyboardType="number-pad"
            />
            <View style={styles.textContainer}>
              <Text style={styles.inputLabel}>{t('Calories burned')}</Text>
              <Text>0 {t('cal')}</Text>
            </View>
          </View>

          <Button
            title={t('Add')}
            containerStyle={styles.saveButton}
            onPress={() => setPushupModal(false)}
          />
        </View>
      </Modal>
      <Modal
        isVisible={aerobicModal}
        animationIn={'slideInUp'}
        animationOut={'slideOutDown'}
        animationInTiming={500}
        animationOutTiming={500}
        onBackdropPress={() => setAerobicModal(false)}
        backdropOpacity={0.3}
        avoidKeyboard
        style={{margin: 0, justifyContent: 'flex-end'}}>
        <View style={[styles.modalContainer]}>
          <View>
            <Text style={styles.modalHeading}>{t('Aerobics')}</Text>
            <View style={styles.divider} />
            <FormInput
              value={minutes}
              label={t('Minutes')}
              onChangeText={v => setMinutes(v)}
              keyboardType="number-pad"
            />
            <View style={styles.textContainer}>
              <Text style={styles.inputLabel}>{t('Calories burned')}</Text>
              <Text>0 {t('cal')}</Text>
            </View>
          </View>
          <Button
            title={t('Add')}
            containerStyle={styles.saveButton}
            onPress={() => setAerobicModal(false)}
          />
        </View>
      </Modal>
      <GeneralModal
        visible={deleteExerciseModal}
        onCancel={() => setDeleteExerciseModal(false)}
        primaryButtonOnpress={() => {
          if (deletingExercise) {
            onPressDeleteExercise(deletingExercise);
          }
          setDeleteExerciseModal(false);
        }}
        topRedTitle={t('Delete Exercise')}
        description={t('You are attempting to delete exercise.')}
        primaryButtonName={t('Delete')}
        secondaryButtonText={t('Cancel')}
      />
    </View>
  );
};

export default Activity;
