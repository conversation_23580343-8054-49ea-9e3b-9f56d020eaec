import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingHorizontal: 24,
    flex: 1,
    justifyContent: 'space-between',
  },
  headingText: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    color: theme.lightColors?.primary,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reviewText: {
    color: '#747474',
    fontSize: 12,
    lineHeight: 15,
    marginTop: 8,
    fontFamily: Fonts.regular,
  },
  connectButton: {
    backgroundColor: theme.lightColors?.grey1,
    borderRadius: 6,
    paddingVertical: 3,
    paddingHorizontal: 10,
    alignSelf: 'flex-end',
    marginTop: 15,
    alignItems: 'center',
  },
  connectText: {
    fontFamily: Fonts.regular,
    color: theme.lightColors?.primary,
    fontSize: 14,
  },
  exerciseContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 24,
    marginBottom: 8,
  },
  descriptionText: {
    fontFamily: Fonts.regular,
    fontSize: 14,
    color: '#666E75',
    lineHeight: 18,
    marginTop: 5,
    width: '90%',
  },
  watchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 12,
    paddingVertical: 7,
    paddingLeft: 7,
    paddingRight: 17,
    marginTop: 31,
  },
  watchImage: {width: 94, height: 114},
  watchHeading: {fontFamily: Fonts.regular, fontSize: 16, color: '#070D02'},
  semiBoldText: {fontFamily: Fonts.semiBold, color: theme.lightColors?.primary},
  arrowImage: {height: 18, width: 18},
  modalHeading: {
    marginTop: 20,
    textAlign: 'center',
    fontFamily: Fonts.semiBold,
    fontSize: 16,
  },
  modalContainer: {
    // height: '45%',
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  divider: {
    borderTopColor: theme.lightColors?.grey2,
    borderTopWidth: 1,
    marginVertical: 18,
    alignSelf: 'center',
    width: '100%',
  },
  textContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  inputLabel: {
    marginBottom: 12,
    fontFamily: Fonts.regular,
    fontSize: 14,
    color: theme.lightColors?.secondary,
  },
  unitText: {fontFamily: Fonts.regular, color: '#2C2C2E80'},
  inputContainer: {
    borderColor: theme.lightColors?.grey1,
    borderRadius: 10,
    borderWidth: 1,
    backgroundColor: '#FAFAFA',
    paddingHorizontal: 10,
    alignSelf: 'center',
    marginTop: 8,
  },
  containerStyles: {
    width: '30%',
    paddingVertical: 0,
    paddingHorizontal: 0,
  },
  deleteFood: {fontFamily: Fonts.semiBold, fontSize: 16},
  deleteFoodContainer: {paddingVertical: 11, paddingHorizontal: 27},
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 40,
  },
  saveButton: {marginBottom: 40, marginTop: 32},
});
