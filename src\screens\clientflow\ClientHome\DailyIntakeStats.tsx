import {View, Text, Image} from 'react-native';
import React, {useRef} from 'react';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import {styles} from './styles';
import {
  netCaloriePercentage,
  netCaloriesLeft,
} from '../../../utilities/app.utils';
import {useTranslation} from 'react-i18next';
import PNGIcons from '../../../assets/pngIcons';
import ProgressBarComponent from '../../../components/ProgressBarComponent/ProgressBarComponent';
import {useAppSelector} from '../../../store';
import useClients from '../../../hooks/models/useClients';

export const DailyIntakeStats: React.FC<any> = () => {
  const {t} = useTranslation();
  const circularProgressRef = useRef(null);
  const {dailyActivity, dailyMacroNutrients, dailyIntakeGoal} = useAppSelector(
    state => state.user,
  );

  return (
    <View>
      <View style={[styles.rowContainer, styles.dailyIntakeGoal]}>
        {/* CONSUMED CALORIES */}
        <View>
          <View style={styles.rowContainer}>
            <Image source={PNGIcons.ForkKnife} style={styles.forkKnife} />
            <Text style={[styles.smallText, {marginLeft: 2}]}>
              {t('Consumed')}
            </Text>
          </View>

          <Text style={styles.calNumber}>
            {dailyMacroNutrients.usedCalories || 0}{' '}
            <Text style={styles.smallText}>{t('cal')}</Text>
          </Text>
        </View>

        {/* CIRCULAR PROGRESS BAR */}

        <View style={styles.circularProgressContainer}>
          <CircularProgressBase
            initialValue={0}
            ref={circularProgressRef}
            activeStrokeWidth={10}
            inActiveStrokeWidth={10}
            inActiveStrokeOpacity={0.2}
            value={netCaloriePercentage(dailyIntakeGoal, dailyMacroNutrients)}
            radius={76}
            activeStrokeColor={'#000'}
            inActiveStrokeColor={'#000000'}
            strokeLinecap={'round'}
            maxValue={100}>
            <Text style={styles.calProgess}>
              {netCaloriesLeft(dailyIntakeGoal, dailyMacroNutrients)}
            </Text>
            <Text style={styles.calLeftText}>{t('calories')}</Text>
          </CircularProgressBase>
        </View>

        {/* BURN CALORIES */}
        <View>
          <View style={styles.rowContainer}>
            <Image source={PNGIcons.Burn} style={styles.forkKnife} />
            <Text style={[styles.smallText, {marginLeft: 2}]}>
              {t('Burned')}
            </Text>
          </View>

          <Text style={styles.calNumber}>
            {dailyActivity?.totalCaloriesBurned || 0}{' '}
            <Text style={styles.smallText}>{t('cal')}</Text>
          </Text>
        </View>
      </View>

      {/* BAR GRAPH INFO */}
      <ProgressBarComponent
        carbsIntake={dailyMacroNutrients.usedCarbs}
        carbsTotal={dailyIntakeGoal.carbs}
        fatsIntake={dailyMacroNutrients.usedFats}
        fatsTotal={dailyIntakeGoal.fat}
        protenIntake={dailyMacroNutrients.usedProtiens}
        proteinTotal={dailyIntakeGoal.proteins}
      />
    </View>
  );
};
