import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React, {useEffect, useLayoutEffect, useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import PNGIcons from '../../../assets/pngIcons';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import {useAppDispatch, useAppSelector} from '../../../store';
import ClientAuthServices from '../../../services/ClientAuthServices';
import {Avatar, Button, FormInput} from '../../../components';
import {USER_PLACEHOLDER} from '../../../utilities/constants';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {AxiosError} from 'axios';
import {useTranslation} from 'react-i18next';
import moment from 'moment';
import {setCoachInfo} from '../../../store/userSlice';
import useApiHandler from '../../../utilities/useApiHandler';
import DetailItem from '../../homeflow/ClientDetail/DetailItem';
import {VerifiedIcon} from '../../../assets/svgIcons';

const validationSchema = Yup.object().shape({
  referralCode: Yup.string().required('Referral Code is required'),
});

type Props = NativeStackScreenProps<ClientStackParamList, 'CoachProfile'>;

const CoachProfile: React.FC<Props> = ({navigation}) => {
  useLayoutEffect(() => {
    navigation.setOptions({
      headerTitle: 'Coach Profile',
    });
  }, [navigation]);
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(state => state.user.user);
  const coach = currentUser.coach;
  const {t} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  // const [coachInfo, setCoachInfo] = useState<IUser>();
  const [loading, setLoading] = useState(true);
  const [loading2, setLoading2] = useState(false);

  const getClient = async () => {
    try {
      setLoading(true);
      const {data} = await ClientAuthServices.GetClientById(currentUser._id);
      dispatch(setCoachInfo(data?.client?.coach));
    } catch (error) {
      console.error('Error fetching client data:', error);
    } finally {
      setLoading(false);
    }
  };

  // ADD COACH REFERRAL LINK TO CLIENT
  async function addReferralCode(referralCode: string) {
    setLoading2(true);
    try {
      const resp = await ClientAuthServices.UpdateClient(currentUser._id, {
        referralCode,
      });

      // SUCCESS - JOIN COACH BY REFERRAL CODE
      if (resp.status === 200) {
        getClient();
      }
    } catch (error) {
      const err = error as AxiosError;
      handleAxiosErrors(err);
    } finally {
      setLoading2(false);
    }
  }

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      if (isMounted) {
        await getClient();
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, []);

  const formik = useFormik({
    initialValues: {
      referralCode: '',
    },
    validationSchema: validationSchema,
    onSubmit: values => addReferralCode(values.referralCode),
  });

  if (coach) {
    return (
      <View style={styles.container}>
        {/* <Text style={styles.headingText}>{t('Coach Profile')}</Text> */}

        <Avatar onPress={() => {}} uri={coach?.photo || USER_PLACEHOLDER} />
        <View style={styles.titleContainer}>
          <Text style={styles.nameText}>{coach?.name}</Text>
          <VerifiedIcon />
        </View>
        {/* <View style={styles.divider} /> */}

        <DetailItem
          title={t('Age')}
          containerStyle={styles.detailItemContainer}
          headingStyle={styles.detailtemHeading}
          data={
            coach?.dob
              ? `${moment().diff(moment(coach.dob), 'years')} ${t('Years')}`
              : ''
          }
        />
        <DetailItem
          title={t('Experience')}
          containerStyle={styles.detailItemContainer}
          headingStyle={styles.detailtemHeading}
          data={coach?.experience ? `${coach.experience} ${t('Years')}` : ''}
        />
        <DetailItem
          title={t('Email')}
          containerStyle={styles.detailItemContainer}
          headingStyle={styles.detailtemHeading}
          data={coach?.email || ''}
        />
        {/* <DetailItem
          title={t('Referal Code')}
          containerStyle={styles.detailItemContainer}
          headingStyle={styles.detailtemHeading}
          data={coach?.referralCode || ''}
        /> */}
        <DetailItem
          title={t('About')}
          containerStyle={styles.detailItemContainer}
          headingStyle={styles.detailtemHeading}
          data={coach?.about || ''}
        />
        <TouchableOpacity
          style={styles.conversationContainer}
          onPress={() => {
            if (coach) {
              const chat = currentUser.chats?.find(c =>
                c.users.includes(coach._id),
              );

              navigation.navigate('ChatDetails', {
                recipient: coach,
                chatId: chat?._id,
              });
            }
          }}>
          <Image
            source={PNGIcons.Conversation}
            style={styles.conversationImage}
          />
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <>
      <View style={styles.container}>
        <Text style={styles.headingText}>{t('Connect to your coach')}</Text>
        <Text style={styles.heading}>
          {t('Enter the referral code provided by your coach')}
        </Text>
        <View style={{marginTop: 30}}>
          <FormInput
            label={t('Referral Code')}
            value={formik.values.referralCode}
            onChangeText={formik.handleChange('referralCode')}
            errorMessage={
              formik.touched.referralCode
                ? t(formik.errors.referralCode as string)
                : undefined
            }
            onBlur={formik.handleBlur('referralCode')}
          />

          <Button
            title={t('Save')}
            onPress={formik.handleSubmit}
            loading={loading2}
            disabled={loading2 || !formik.isValid}
            containerStyle={{marginTop: 10}}
          />
        </View>
      </View>
    </>
  );
};

export default CoachProfile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.white,
  },
  divider: {
    borderTopColor: theme.lightColors?.grey2,
    borderTopWidth: 1,
    marginVertical: 18,
    alignSelf: 'center',
    width: '100%',
  },
  description: {
    marginTop: 6,
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    letterSpacing: 0.5,
    color: theme.lightColors?.secondary,
  },
  heading: {
    fontFamily: Fonts.regular,
    color: '#707070',
    fontSize: 12,
    marginTop: 16,
  },
  conversationImage: {
    height: 60,
    width: 60,
  },
  conversationContainer: {position: 'absolute', right: 24, bottom: 30},
  nameText: {
    // marginTop: 12,
    textAlign: 'center',
    fontFamily: Fonts.semiBold,
    fontSize: 20,
    letterSpacing: 0.5,
    lineHeight: 27,
    color: theme.lightColors?.black,
  },
  profileImage: {
    width: 100,
    height: 100,
    alignSelf: 'center',
    borderRadius: 50,
  },
  headingText: {
    fontSize: 18,
    fontFamily: Fonts.bold,
    color: theme.lightColors?.black,
  },
  detailtemHeading: {
    marginTop: 0,
  },
  detailItemContainer: {
    marginBottom: 5,
    paddingLeft: 10,
    paddingVertical: 12,
    gap: 10,
  },
  titleContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: 20,
    gap: 4,
  },
  // iconStyle: {
  //   width: 25,
  //   height: 25,
  // },
});
