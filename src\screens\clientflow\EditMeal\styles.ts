import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 14,
    backgroundColor: theme.lightColors?.white,
    justifyContent: 'space-between',
  },
  proteinText: {
    fontSize: 14,
    fontFamily: Fonts.semiBold,
    color: '#FFA047',
    marginTop: 8,
  },
  calLeftText: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
  },
  mealList: {
    backgroundColor: '#FAFAFA',
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    paddingHorizontal: 12,
    borderRadius: 10,
    marginTop: 35,
  },
  servingCount: {
    justifyContent: 'center',
    marginTop: 0,
    width: '17%',
  },
  mealName: {fontFamily: Fonts.regular, fontSize: 14},
  servingContainer: {
    flexDirection: 'row',
    marginTop: 12,
    justifyContent: 'space-between',
  },
  arrowIcon: {height: 20, width: 20},
  regularFont: {fontFamily: Fonts.regular, fontSize: 14},
  headingText: {fontFamily: Fonts.semiBold, fontSize: 14, marginTop: 20},
  nutritionContainer: {
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    marginTop: 14,
    paddingVertical: 15,
    paddingHorizontal: 12,
    borderRadius: 10,
  },
  caloryText: {
    alignSelf: 'center',
    textAlign: 'center',
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.primary,
  },
  calUnitText: {fontFamily: Fonts.regular, fontSize: 12},
  progressIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 25,
  },
  servingList: {
    width: '80%',
    marginTop: 0,
  },
  deleteFood: {fontFamily: Fonts.semiBold, fontSize: 16},
  deleteFoodContainer: {paddingVertical: 11, paddingHorizontal: 27},
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 40,
  },
  saveButton: {width: '90%', marginLeft: 13},
});
