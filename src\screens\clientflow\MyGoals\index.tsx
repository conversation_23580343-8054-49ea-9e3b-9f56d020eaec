import {Text, View} from 'react-native';
import React, {useRef} from 'react';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import * as Progress from 'react-native-progress';
import {Button} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import {useAppSelector} from '../../../store';
import {
  netCaloriePercentage,
  netCaloriesLeft,
} from '../../../utilities/app.utils';
import {useTranslation} from 'react-i18next';
import {styles} from './styles';
import {sizes, theme} from '../../../utilities/theme';

type Props = NativeStackScreenProps<ClientStackParamList, 'MyGoals'>;

const MyGoals: React.FC<Props> = ({navigation}) => {
  const circularProgressRef = useRef(null);
  const userData = useAppSelector(state => state.user);
  const dailyIntake = userData?.dailyIntakeGoal;
  const dailyConsumption = userData.dailyMacroNutrients;
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      {dailyIntake ? (
        <>
          <View>
            <Text style={styles.heading}>{t('Daily Calories Goal')}</Text>
            <View style={styles.circularProgressContainer}>
              <CircularProgressBase
                initialValue={0}
                ref={circularProgressRef}
                activeStrokeWidth={10}
                inActiveStrokeWidth={10}
                inActiveStrokeOpacity={0.2}
                value={netCaloriePercentage(dailyIntake, dailyConsumption)}
                radius={69}
                activeStrokeColor={'#000'}
                inActiveStrokeColor={'#000000'}
                strokeLinecap={'round'}
                maxValue={100}>
                <Text style={styles.calProgess}>
                  {netCaloriesLeft(dailyIntake, dailyConsumption) || 0}
                </Text>
                <Text style={styles.calLeftText}>{t('calories')}</Text>
              </CircularProgressBase>
            </View>
            <Text style={styles.heading}>{t('Daily Nutrients Goal')}</Text>
            <View style={styles.verticalProgressContainer}>
              {/* PROTEINS */}
              <View
                style={{
                  flexDirection: 'row',
                  width: sizes.deviceWidth * 0.31,
                }}>
                <View style={{transform: [{rotate: '-90deg'}]}}>
                  <Progress.Bar
                    progress={
                      dailyConsumption.usedProtiens / dailyIntake.proteins || 0
                    }
                    height={8}
                    width={53}
                    animated
                    unfilledColor="#2828281C"
                    color={theme.lightColors?.primary}
                    borderColor="transparent"
                  />
                </View>
                <View style={{marginLeft: -30}}>
                  <Text style={styles.typeText}>{t('Proteins')}</Text>
                  <Text style={styles.calText}>
                    {dailyConsumption.usedProtiens}g /
                    <Text style={styles.totalCal}>{dailyIntake.proteins}g</Text>
                  </Text>
                </View>
              </View>

              {/* CARBS */}
              <View
                style={{
                  flexDirection: 'row',
                  width: sizes.deviceWidth * 0.31,
                }}>
                <View style={{transform: [{rotate: '-90deg'}]}}>
                  <Progress.Bar
                    progress={
                      dailyConsumption.usedCarbs / dailyIntake.carbs || 0
                    }
                    height={8}
                    width={53}
                    animated
                    unfilledColor="#2828281C"
                    color={theme.lightColors?.primary}
                    borderColor="transparent"
                  />
                </View>
                <View style={{marginLeft: -30}}>
                  <Text style={styles.typeText}>{t('Carbs')}</Text>
                  <Text style={styles.calText}>
                    {dailyConsumption.usedCarbs}g /
                    <Text style={styles.totalCal}>{dailyIntake.carbs}g</Text>
                  </Text>
                </View>
              </View>

              {/* FATS */}
              <View
                style={{
                  flexDirection: 'row',
                  width: sizes.deviceWidth * 0.31,
                }}>
                <View style={{transform: [{rotate: '-90deg'}]}}>
                  <Progress.Bar
                    progress={dailyConsumption.usedFats / dailyIntake.fat || 0}
                    height={8}
                    width={53}
                    animated
                    unfilledColor="#2828281C"
                    color={theme.lightColors?.primary}
                    borderColor="transparent"
                  />
                </View>
                <View style={{marginLeft: -30}}>
                  <Text style={styles.typeText}>{t('Fats')}</Text>
                  <Text style={styles.calText}>
                    {dailyConsumption.usedFats}g /
                    <Text style={styles.totalCal}>{dailyIntake.fat}g</Text>
                  </Text>
                </View>
              </View>
            </View>
          </View>
          <Button
            title={t('Edit Goals')}
            containerStyle={{marginBottom: 30}}
            onPress={() =>
              navigation.navigate('EditGoals', {goal: dailyIntake})
            }
          />
        </>
      ) : (
        <View style={{marginTop: 100, alignItems: 'center'}}>
          <Text style={styles.goalText}>{t('No Goal set yet')}</Text>
          <Text style={styles.contactText}>{t('Contact your coach')}</Text>
        </View>
      )}
    </View>
  );
};

export default MyGoals;
