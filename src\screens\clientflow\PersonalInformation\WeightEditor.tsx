import React, {useEffect, useState} from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Dimensions,
} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';
import {useTranslation} from 'react-i18next';
import {RulerPicker} from '../../../components/RulerPicker';

const {height} = Dimensions.get('window');
const BOTTOM_SHEET_HEIGHT = height * 0.55; // 55% of screen height

interface WeightEditorProps {
  initialWeight?: string;
  onSave: (weight: string) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const WeightEditor: React.FC<WeightEditorProps> = ({
  initialWeight,
  onSave,
  onCancel,
  isLoading = false,
}) => {
  const [unit, setUnit] = useState<'kg' | 'lb'>('kg');
  const [weight, setWeight] = useState('');
  const {t} = useTranslation();

  // Parse initial weight value
  useEffect(() => {
    if (initialWeight) {
      const parts = initialWeight.split(' ');
      if (parts.length >= 2) {
        const weightUnit = parts[1] as 'kg' | 'lb';
        const weightValue = parts[0];
        setUnit(weightUnit);
        setWeight(weightValue);
      }
    }
  }, [initialWeight]);

  const handleSave = () => {
    if (weight) {
      onSave(`${weight} ${unit}`);
    }
  };

  const isValid = !!weight;

  return (
    <View style={[styles.container, {maxHeight: BOTTOM_SHEET_HEIGHT}]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onCancel}>
          <Text style={styles.cancelText}>{t('Cancel')}</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('Edit Weight')}</Text>
        <TouchableOpacity onPress={handleSave} disabled={!isValid || isLoading}>
          <Text
            style={[
              styles.saveText,
              (!isValid || isLoading) && styles.disabledText,
            ]}>
            {t('Save')}
          </Text>
        </TouchableOpacity>
      </View>
      {/* Content */}
      <View style={styles.content}>
        <Text style={styles.headingText}>
          {t('What is your current weight?')}
        </Text>

        <View>
          <RulerPicker
            min={0}
            max={300}
            step={1}
            fractionDigits={0}
            initialValue={parseFloat(weight) || 0}
            onValueChange={number => setWeight(number.toString())}
            unit={unit}
            indicatorColor={theme.lightColors?.black}
            stepWidth={3} // Slimmer steps
            indicatorHeight={90} // Shorter indicator
            decelerationRate={'fast'}
            gapBetweenSteps={2} // Tighter spacing
            shortStepHeight={18} // Smaller steps
            shortStepColor="#D7D8D9"
            longStepHeight={50} // Smaller long steps
            longStepColor="#BABBBE"
            height={300} // Compact ruler
            unitTextStyle={{
              fontSize: 30, // Smaller unit text
              color: '#676C75',
              marginTop: 20,
            }}
            valueTextStyle={{
              fontSize: 60, // Smaller value text
              color: theme.lightColors?.black,
              marginTop: -5,
            }}
          />

          <View style={styles.unitSelector}>
            <TouchableOpacity
              style={[
                styles.unitButton,
                {
                  backgroundColor:
                    unit === 'kg'
                      ? theme.lightColors?.primary
                      : theme.lightColors?.grey1,
                },
              ]}
              onPress={() => setUnit('kg')}>
              <Text
                style={{
                  color: unit === 'kg' ? theme.lightColors?.white : '#969696',
                  fontSize: 14,
                  fontFamily: Fonts.medium,
                }}>
                kg
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.unitButton,
                {
                  backgroundColor:
                    unit === 'lb'
                      ? theme.lightColors?.primary
                      : theme.lightColors?.grey1,
                },
              ]}
              onPress={() => setUnit('lb')}>
              <Text
                style={{
                  color: unit === 'lb' ? theme.lightColors?.white : '#969696',
                  fontSize: 14,
                  fontFamily: Fonts.medium,
                }}>
                lb
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.lightColors?.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12, // Smaller header
    borderBottomWidth: 1,
    borderBottomColor: theme.lightColors?.grey1,
  },
  headerTitle: {
    fontSize: 16, // Smaller title
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.black,
  },
  cancelText: {
    fontSize: 14, // Smaller text
    fontFamily: Fonts.medium,
    color: theme.lightColors?.grey0,
  },
  saveText: {
    fontSize: 14, // Smaller text
    fontFamily: Fonts.medium,
    color: theme.lightColors?.primary,
  },
  disabledText: {
    color: theme.lightColors?.grey1,
  },
  content: {
    paddingHorizontal: 20, // Tighter padding
    paddingVertical: 10,
  },
  headingText: {
    fontSize: 18, // Slightly smaller
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
    textAlign: 'center',
    marginBottom: 15, // Reduced spacing
  },

  unitSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10, // Smaller gap
    marginTop: 20, // Reduced spacing
  },
  unitButton: {
    paddingVertical: 6, // Smaller buttons
    paddingHorizontal: 12,
    borderRadius: 5,
  },
});

export default WeightEditor;
