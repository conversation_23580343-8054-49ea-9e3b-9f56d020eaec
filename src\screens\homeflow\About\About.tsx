import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import images from '../../../assets/images';
import {t} from 'i18next';

const About = () => {
  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* LOGO */}
          <Image
            source={images.logo}
            style={{
              width: 100,
              height: 100,
              alignSelf: 'center',
              marginTop: 27,
            }}
          />
          {/* PARAGRPH */}
          <Text style={styles.paragraph}>
            {t(
              "Welcome to Food Log, the ultimate nutrition tracking app designed to help you take control of your health and achieve your wellness goals.\n\n Whether you're looking to lose weight, gain muscle, or simply maintain a balanced diet, Food Log makes it easy to track your meals, monitor your progress, and stay on top of your nutrition.\n\n Our app offers a comprehensive suite of features to help you log your food intake, track your daily calories and macronutrients, and stay motivated along the way.\n\nWith a user-friendly interface and personalized recommendations, Food Log is your trusted companion on your health journey.",
            )}
          </Text>
          <Text style={[styles.paragraph, styles.paragraphHeading]}>
            {t('Why Choose Food Log?')}
          </Text>
          <View style={styles.textRowContainer}>
            <Text style={styles.paragraph}>•</Text>
            <Text style={styles.paragraph}>
              {t(
                'Easy to Use: Designed with simplicity in mind, Food Log is intuitive and easy to navigate',
              )}
            </Text>
          </View>
          <View style={styles.textRowContainer}>
            <Text style={styles.paragraph}>•</Text>
            <Text style={styles.paragraph}>
              {t(
                'Accurate Data: We provide reliable nutritional data sourced from trusted food databases and verified nutrition labels.',
              )}
            </Text>
          </View>
          <View style={styles.textRowContainer}>
            <Text style={styles.paragraph}>•</Text>
            <Text style={styles.paragraph}>
              {t(
                'Motivation & Support: Stay motivated with the help of your specialist who will',
              )}
            </Text>
          </View>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default About;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingBottom: 16,
    paddingHorizontal: 24,
  },
  paragraph: {
    fontSize: 13,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
    paddingTop: 20,
  },
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  paragraphHeading: {
    fontWeight: 'bold',
  },
  textRowContainer: {
    flex: 1,
    flexDirection: 'row',
    gap: 10,
  },
});
