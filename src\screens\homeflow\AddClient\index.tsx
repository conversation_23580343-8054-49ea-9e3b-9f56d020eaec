import {StyleSheet, Text, View} from 'react-native';
import React, {useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {Button, FormInput, ModalSuccess} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import InviteServices from '../../../services/InviteServices';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {AxiosError} from 'axios';
import Toast from 'react-native-toast-message';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<HomeStackParamList, 'AddClient'>;

const AddClient: React.FC<Props> = ({navigation}) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const {t} = useTranslation();

  const validationSchema = Yup.object().shape({
    name: Yup.string().required(t('Name is required')),
    email: Yup.string()
      .email(t('Invalid email'))
      .required(t('Email is required')),
  });
  const handleSendInvite = async () => {
    try {
      setIsLoading(true);
      const resp = await InviteServices.SendInvites({
        name: formik.values.name,
        clientEmail: formik.values.email.toLowerCase(),
      });
      if (resp.status == 201) {
        setModalVisible(true);
        setTimeout(() => {
          setModalVisible(false);

          setTimeout(() => {
            navigation.goBack();
          }, 1000);
        }, 3000);
      }
    } catch (error: any) {
      console.log('error in sending invite', error?.response?.data);
      setIsLoading(false);
      let errorMessage = error?.response?.data?.message || '';
      if (
        error?.response.data.message === 'A duplicate entry already exists.'
      ) {
        errorMessage = t('This client is already linked to a different coach');
      }

      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
    },
    validateOnMount: true,
    validationSchema: validationSchema,
    onSubmit: handleSendInvite,
  });
  return (
    <View style={styles.container}>
      <View>
        <Text style={styles.headerText}>
          {t('Add your client through email')}
        </Text>
        <FormInput
          label={t('Name')}
          onChangeText={formik.handleChange('name')}
          onBlur={formik.handleBlur('name')}
          value={formik.values.name}
          errorMessage={
            formik.touched.name ? t(formik.errors.name as string) : undefined
          }
        />
        <FormInput
          label={t('Email')}
          onChangeText={formik.handleChange('email')}
          onBlur={formik.handleBlur('email')}
          value={formik.values.email}
          errorMessage={
            formik.touched.email ? t(formik.errors.email as string) : undefined
          }
        />
      </View>
      <Button
        title={t('Send Invite')}
        containerStyle={{marginBottom: 40}}
        onPress={formik.handleSubmit}
        disabled={!formik.isValid || isLoading}
        loading={isLoading}
      />
      <ModalSuccess
        isModalVisible={isModalVisible}
        setModalVisible={setModalVisible}
        paragraph={t(
          'Your client is added successfully and also invite sent via email.',
        )}
      />
    </View>
  );
};

export default AddClient;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: theme.lightColors?.background,
    justifyContent: 'space-between',
  },
  headerText: {
    marginVertical: 35,
    fontFamily: Fonts.regular,
    fontSize: 16,
    color: theme.lightColors?.black,
  },
});
