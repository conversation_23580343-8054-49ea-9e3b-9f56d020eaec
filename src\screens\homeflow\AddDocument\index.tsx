import {Platform, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {Button, FormInput} from '../../../components';
import {AddGrayIcon} from '../../../assets/svgIcons';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import {styles} from './styles';
import {
  DocumentPickerResponse,
  pick,
  types,
} from 'react-native-document-picker';
import {useFormik} from 'formik';
import * as yup from 'yup';
import Toast from 'react-native-toast-message';
import DocumentServices from '../../../services/DocumentServices';
import {useAppDispatch, useAppSelector} from '../../../store';
import {useTranslation} from 'react-i18next';
import {addNewDocument} from '../../../store/DocumentsSlice';
type Props = NativeStackScreenProps<HomeStackParamList, 'AddDocument'>;

const AddDocument: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();

  const validationSchema = yup.object().shape({
    name: yup.string().required(t('Document Name is required')),
    description: yup.string().required(t('Document Description is required')),
    selectedDocument: yup
      .string()
      .required(t('You need to select a document first')),
  });
  const user = useAppSelector(state => state.user.user);
  const [selectedFile, setSelectedFile] = useState<DocumentPickerResponse>();
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useAppDispatch();

  const selectFile = async () => {
    const [result] = await pick({type: types.pdf, copyTo: 'cachesDirectory'});
    formik.setFieldValue('selectedDocument', result.uri);
    setSelectedFile(result);
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      const docUrl = await handleUpload();
      const payload = {
        title: formik.values.name,
        description: formik.values.description,
        docUrl,
      };
      const resp = await DocumentServices.CreateDocument(user._id, payload);

      // SUCCESS
      if (resp.status == 201) {
        dispatch(addNewDocument(resp.data));
        navigation.goBack();
        Toast.show({
          type: 'success',
          text1: t('Success'),
          text2: t('New document added successfully'),
        });
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Error while creating a document'),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpload = async () => {
    try {
      let data = new FormData();
      data.append('file', {
        name: selectedFile?.name,
        type: selectedFile?.type,
        uri:
          Platform.OS === 'android'
            ? selectedFile?.uri
            : selectedFile?.uri.replace('file://', ''),
      });

      const resp = await DocumentServices.UploadDocument(data);

      if (resp.status == 201) {
        return resp.data.url;
      }
    } catch (error) {
      return Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t("Couldn't upload the file."),
      });
    }
  };

  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      selectedDocument: '',
    },
    validationSchema: validationSchema,
    validateOnMount: true,
    onSubmit: handleSave,
  });
  return (
    <View style={styles.container}>
      <View>
        <FormInput
          placeholder={t('Name')}
          label={t('Name')}
          labelStyle={{marginTop: 35}}
          value={formik.values.name}
          onChangeText={formik.handleChange('name')}
          onBlur={formik.handleBlur('name')}
          errorMessage={
            formik.touched.name ? t(formik.errors.name as string) : undefined
          }
        />

        <FormInput
          placeholder={t('Description')}
          label={t('Description')}
          value={formik.values.description}
          onChangeText={formik.handleChange('description')}
          onBlur={formik.handleBlur('description')}
          errorMessage={
            formik.touched.description
              ? t(formik.errors.description as string)
              : undefined
          }
          multiline
        />

        {formik.values.selectedDocument ? (
          <>
            <View style={styles.uploadContainer}>
              <Text style={styles.fileName}>{selectedFile?.name}</Text>
              {selectedFile?.size && (
                <Text style={styles.fileSize}>
                  {(selectedFile?.size / 1024).toFixed(2)} kb
                </Text>
              )}
            </View>
          </>
        ) : (
          <>
            <TouchableOpacity
              style={styles.uploadContainer}
              onPress={selectFile}>
              <AddGrayIcon />
              <Text style={styles.uploadText}>{t('Upload document')}</Text>
            </TouchableOpacity>
            {formik.touched.selectedDocument &&
              formik.errors.selectedDocument && (
                <Text style={styles.errorText}>
                  {formik.errors.selectedDocument}
                </Text>
              )}
          </>
        )}
      </View>
      <Button
        title={t('Save')}
        containerStyle={{marginBottom: 40}}
        onPress={formik.handleSubmit}
        loading={isLoading}
        disabled={isLoading || !formik.isValid}
      />
    </View>
  );
};

export default AddDocument;
