import {
  ActivityIndicator,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {ListItem, SearchBar} from '../../../components';
import GeneralModal from '../../../components/Modals/GeneralModal/GeneralModal';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import useClients from '../../../hooks/models/useClients';
import {useTranslation} from 'react-i18next';
type Props = NativeStackScreenProps<HomeStackParamList, 'Archive'>;

const Archive: React.FC<Props> = ({navigation}) => {
  const [searchString, setSearchString] = useState('');
  const [isModalVisible, setModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [menuOpened, setMenuOpened] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [clientID, setClientId] = useState('');
  const {handleDeleteClient, archivedClients, handleArchiveClient} =
    useClients();
  const {t} = useTranslation();

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        {isLoading ? (
          <ActivityIndicator
            size={'large'}
            color={theme.lightColors?.primary}
            style={{marginTop: 300}}
          />
        ) : (
          <>
            <SearchBar
              value={searchString}
              onChangeText={txt => setSearchString(txt)}
            />
            <Text style={styles.clientText}>
              {t('Total clients:')}{' '}
              <Text style={styles.clientText2}>{archivedClients?.length}</Text>
            </Text>

            {archivedClients?.length ? (
              <ScrollView
                contentContainerStyle={{paddingBottom: 2}}
                showsVerticalScrollIndicator={false}>
                {archivedClients.map((item, index) => {
                  return (
                    <ListItem
                      key={index}
                      name={item.name}
                      avatar={item.image}
                      opened={menuOpened}
                      setOpened={() => setMenuOpened(!menuOpened)}
                      data={[
                        {
                          id: 1,
                          label: t('Unarchive'),
                          onPress: () => {
                            handleArchiveClient(item._id, false);
                          },
                        },
                        {
                          id: 2,
                          label: t('Delete'),

                          onPress: () => {
                            setModalVisible(true);
                            setClientId(item._id);
                          },
                          style: styles.deleteText,
                        },
                      ]}
                    />
                  );
                })}
              </ScrollView>
            ) : (
              <Text style={styles.emptyText}>{t('No archived client')}</Text>
            )}
          </>
        )}
      </View>
      <GeneralModal
        visible={isModalVisible}
        onCancel={() => setModalVisible(false)}
        primaryButtonOnpress={() => {
          handleDeleteClient(clientID);
          setClientId('');
          setModalVisible(false);
        }}
        topRedTitle={t('Delete Client')}
        description={t('You are attempting to delete client.')}
        primaryButtonName={t('Yes, Delete')}
        secondaryButtonText={t('Cancel')}
        loading={deleteLoading}
        disabled={deleteLoading}
      />
    </SafeAreaView>
  );
};

export default Archive;

const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
  },
  clientText: {
    fontSize: 12,
    color: '#A0A0A0',
    fontFamily: Fonts.regular,
    marginBottom: 18,
    alignItems: 'center',
  },
  clientText2: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
  },
  emptyText: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    textAlign: 'center',
    color: 'black',
  },
  deleteText: {
    color: theme.lightColors?.error,
  },
});
