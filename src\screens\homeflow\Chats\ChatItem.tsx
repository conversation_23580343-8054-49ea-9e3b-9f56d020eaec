import {StyleSheet, View} from 'react-native';
import React from 'react';
import {ListItem} from '@rneui/themed';
import {Avatar, Divider} from '@rneui/base';
import {Fonts, theme} from '../../../utilities/theme';
import {IChat} from '../../../interfaces/IChat';
import {useAppSelector} from '../../../store';
import moment from 'moment';
import {USER_PLACEHOLDER} from '../../../utilities/constants';
import i18n from '../../../i18n/i18n';
import {VerifiedIcon} from '../../../assets/svgIcons';

interface Props {
  chatItem: IChat;
  onPress: () => void;
  unReadMsgCount: number;
}

const ChatItem: React.FC<Props> = ({chatItem, onPress, unReadMsgCount}) => {
  const currentUser = useAppSelector(({user}) => user.user);

  const sender = chatItem.users.find(u => u._id !== currentUser._id);

  moment.locale(i18n.language);
  return (
    <View>
      <ListItem containerStyle={styles.itemContainer} onPress={onPress}>
        <Avatar
          containerStyle={styles.avatarContainer}
          rounded
          source={{
            uri: sender?.image || sender?.photo || USER_PLACEHOLDER,
          }}
          imageProps={{borderRadius: 50}}
        />
        <ListItem.Content style={{marginLeft: 0, paddingLeft: 0}}>
          <View style={styles.rowContainer}>
            <ListItem.Title style={styles.name}>{sender?.name}</ListItem.Title>
            {sender?.userType === 'coach' ? (
              <VerifiedIcon width={16} height={16} />
            ) : null}
          </View>
          <ListItem.Title style={styles.messageText} numberOfLines={1}>
            {chatItem.lastMessage.content}
          </ListItem.Title>
        </ListItem.Content>
        <ListItem.Content style={{alignItems: 'flex-end'}}>
          <ListItem.Title style={styles.timeText}>
            {moment(chatItem.lastMessage.createdAt).format('LT')}
          </ListItem.Title>

          {unReadMsgCount ? (
            <Avatar
              size={18}
              rounded
              title={unReadMsgCount.toString()}
              containerStyle={styles.containerStyle}
            />
          ) : null}

          {/* {chatItem?.check ? (
            <Check
              width={14}
              height={14}
              style={{marginRight: 16, marginTop: 4}}
              stroke="#898989"
            />
          ) : chatItem?.doubleCheck ? (
            <CheckDouble style={{marginRight: 16, marginTop: 4}} />
          ) : (
            <Avatar
              size={18}
              rounded
              title="1"
              containerStyle={styles.containerStyle}
            />
          )} */}
        </ListItem.Content>
      </ListItem>
      <Divider style={styles.divider} />
    </View>
  );
};

export default ChatItem;

const styles = StyleSheet.create({
  itemContainer: {
    paddingHorizontal: 0,
    paddingVertical: 0,
    backgroundColor: theme.lightColors?.white,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 12,
  },
  name: {
    fontSize: 14,
    fontFamily: Fonts.semiBold,
    color: 'black',
  },
  messageText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}70`,
    fontFamily: Fonts.medium,
    paddingTop: 4,
  },
  timeText: {
    fontSize: 12,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.secondary,
  },
  containerStyle: {
    backgroundColor: theme.lightColors?.primary,
    marginTop: 6,
    marginRight: 14,
  },
  divider: {marginVertical: 10, width: '83%', alignSelf: 'flex-end'},
  avatarContainer: {width: 50, height: 50, borderRadius: 50},
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
});
