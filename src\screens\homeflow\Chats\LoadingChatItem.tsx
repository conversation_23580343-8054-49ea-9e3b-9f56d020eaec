import {Skeleton} from '@rneui/base';
import React from 'react';
import {View, StyleSheet, FlatList} from 'react-native';

const SkeletonChatItem = () => (
  <>
    <View style={styles.chatItem}>
      <Skeleton circle width={50} height={50} />
      <View style={styles.textContainer}>
        <Skeleton width="50%" height={16} style={styles.marginBottom} />
        <Skeleton width="70%" height={10} />
      </View>

      <View style={styles.rightContainer}>
        <Skeleton width={40} height={15} style={styles.marginBottom} />
        <Skeleton circle width={20} height={20} />
      </View>
    </View>
    <View style={{flex: 1, alignItems: 'flex-end'}}>
      <Skeleton width="82%" height={1} style={styles.separator} />
    </View>
  </>
);

const LoadingChatItem = () => {
  const skeletonData = Array(10).fill({});

  return (
    <FlatList
      data={skeletonData}
      keyExtractor={(item, index) => index.toString()}
      renderItem={() => <SkeletonChatItem />}
    />
  );
};

const styles = StyleSheet.create({
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    backgroundColor: '#fff',
  },
  textContainer: {
    flex: 1,
    marginLeft: 20,
  },
  rightContainer: {
    alignItems: 'flex-end',
  },
  marginBottom: {
    marginBottom: 5,
  },
  separator: {
    marginTop: 0,
    marginBottom: 0,
  },
});

export default LoadingChatItem;
