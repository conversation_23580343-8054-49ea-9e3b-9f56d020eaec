import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
  },
  divider: {
    borderTopColor: theme.lightColors?.grey2,
    borderTopWidth: 1,
    marginVertical: 18,
    alignSelf: 'center',
    width: '100%',
  },
  title: {
    fontFamily: Fonts.semiBold,
    fontSize: 16,
    color: theme.lightColors?.black,
  },
  nameText: {
    textAlign: 'center',
    marginTop: 18,
    fontSize: 20,
    fontFamily: Fonts.semiBold,
    lineHeight: 27,
    letterSpacing: 0.5,
    color: theme.lightColors?.black,
  },
  profileImage: {
    height: 100,
    width: 100,
    alignSelf: 'center',
    marginTop: 18,
    borderRadius: 50,
    objectFit: 'cover',
  },
  titleContainer: {flexDirection: 'row', justifyContent: 'space-between'},
  typeText: {fontFamily: Fonts.bold, fontSize: 14, lineHeight: 18},
  calText: {
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    lineHeight: 18,
    marginTop: 8,
  },
  totalCal: {fontFamily: Fonts.regular, fontSize: 12, lineHeight: 15},
  calLeftText: {
    fontFamily: Fonts.regular,
    fontSize: 14,
    lineHeight: 19,
    color: theme.lightColors?.black,
  },
  calProgess: {
    fontFamily: Fonts.semiBold,
    fontSize: 20,
    color: theme.lightColors?.black,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 18,
  },
  smallHeading: {
    lineHeight: 16,
    marginTop: 4,

    fontFamily: Fonts.semiBold,
    fontSize: 14,
    color: '#2C2C2E',
  },
  infoText: {
    color: '#707070',
    fontSize: 12,
    fontFamily: Fonts.regular,
    lineHeight: 19,
  },
  conversationContainer: {position: 'absolute', right: 24, bottom: 30},
  conversationImage: {
    height: 60,
    width: 60,
  },
  deleteText: {
    color: theme.lightColors?.error,
  },
});
