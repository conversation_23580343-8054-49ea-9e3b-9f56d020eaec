import {ActivityIndicator, Image, ScrollView, Text, View} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import PNGIcons from '../../../assets/pngIcons';
import CalendarStrip from 'react-native-calendar-strip';
import MealInfo from '../../../components/MealInfo/MealInfo';
import {styles} from './styles';
import WeightTrackerGraph from '../../../components/WeightTrackerGraph/WeightTrackerGraph';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import moment from 'moment';
import MealServices from '../../../services/MealServices';
import {AxiosError} from 'axios';
import {IMealPost} from '../../../interfaces/IMeal';
import WeightServices from '../../../services/WeightServices';
import {IWeight} from '../../../interfaces/IWeight';
import {useTranslation} from 'react-i18next';
import ProgressBarComponent from '../../../components/ProgressBarComponent/ProgressBarComponent';
import {Divider} from '@rneui/base';
import {IGoal, INutrients} from '../../../interfaces/IUser.';
import ClientAuthServices from '../../../services/ClientAuthServices';
import {IClientDetails} from '../../../interfaces/Clients';
import useActivity from '../../../hooks/models/useActivity';
import {IActivity} from '../../../interfaces/IActivity';
type Props = NativeStackScreenProps<HomeStackParamList, 'ClientHistory'>;

const ClientHistory: React.FC<Props> = ({navigation, route}) => {
  const {t} = useTranslation();

  const {clientId} = route.params;
  const [isLoading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(moment());

  const [mealPosts, setMealPosts] = useState<IMealPost[]>([]);
  const [clientGoal, setClientGoal] = useState<IGoal>();
  const [dailyActivity, setDailyActivity] = useState<IActivity>();
  const [currentNutrients, setCurrentNutrients] = useState<INutrients>({
    usedCalories: 0,
    usedCarbs: 0,
    usedFats: 0,
    usedProtiens: 0,
  });
  const [clientWeights, setClientWeights] = useState<IWeight[]>();
  const {fetchDailyActivity} = useActivity();
  const formatMealTime = (timestamp: string) => {
    if (!timestamp) return t('No date available');

    // Handle both string and number timestamps
    const numericTimestamp =
      typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;

    const momentDate = moment(numericTimestamp);

    if (!momentDate.isValid()) {
      console.log('Invalid timestamp:', timestamp);
      return t('Invalid Date');
    }

    const now = moment();
    let dayLabel = momentDate.isSame(now, 'day')
      ? t('Today')
      : momentDate.isSame(now.clone().subtract(1, 'day'), 'day')
      ? t('Yesterday')
      : momentDate.format('MMM Do, YYYY');

    return `${dayLabel} - ${momentDate.format('h:mm a')}`;
  };
  /* --- GET CLIENT MEALS W.R.T DATE ---*/
  async function getClientMealsWithDate() {
    setLoading(true);
    const currentDate = selectedDate.format('YYYY-MM-DD');
    try {
      const resp = await MealServices.GetMealsByClient(clientId, currentDate);
      const meals = resp.data;
      // SUCCESS GET
      if (resp.status === 200) {
        setMealPosts(resp.data as IMealPost[]);
        let mealData = [];
        let usedData = {
          usedCalories: 0,
          usedProtiens: 0,
          usedCarbs: 0,
          usedFats: 0,
        };

        if (meals.length) {
          for (const meal of meals) {
            let foods = [];
            if (meal.mealFoods.length) {
              for (const food of meal.mealFoods) {
                usedData.usedCalories += food.calories;
                usedData.usedProtiens += food.proteins;
                usedData.usedCarbs += food.carbs;
                usedData.usedFats += food.fats;

                foods.push({...food, serving: food.serving});
              }
            }
            mealData.push({...meal, mealFoods: foods});
          }
        }

        setCurrentNutrients(usedData);
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('COACH: CLIENT HISTORY -- ', err.response?.data);
    } finally {
      setLoading(false);
    }
  }

  /* --- GET CLIENT WEIGHTS ---*/
  async function getClientsWeights() {
    try {
      const resp = await WeightServices.GetWeightByClient(clientId);

      // SUCCESS - GET WEIGHTS
      if (resp.status === 200) {
        setClientWeights(resp.data as IWeight[]);
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log(
        'COACH > CLEINT HISTORY > GET WEIGHTS -> ',
        err.response?.data,
      );
    }
  }

  /* -- GET CLIENT DETAILS */
  async function getClientDetails() {
    await ClientAuthServices.GetClientById(clientId)
      .then(res => {
        if (res.data.goal) setClientGoal(res.data.goal);
      })
      .catch(error => console.log('ERR while fetching client details by ID'));
  }

  async function getActivity(date: Date) {
    console.log('Date>>>', date.toDateString());

    const activities = await fetchDailyActivity(clientId, date, false);
    if (activities.length) {
      setDailyActivity(activities[0]);
    }
  }

  useEffect(() => {
    getClientMealsWithDate();
    if (selectedDate) getActivity(new Date(selectedDate.format()));
  }, [selectedDate]);

  useEffect(() => {
    getClientsWeights();
    getClientDetails();
  }, []);
  return (
    <View style={styles.container}>
      <ScrollView
        contentContainerStyle={{paddingBottom: 50}}
        showsVerticalScrollIndicator={false}>
        {clientWeights?.length ? (
          <>
            <WeightTrackerGraph
              weights={clientWeights}
              onPress={() => navigation.navigate('WeightProgress')}
            />
          </>
        ) : null}
        <View style={styles.monthNameContainer}>
          <Text style={styles.monthName}>{selectedDate.format('MMMM')}</Text>
          <Image source={PNGIcons.ArrowDown} style={styles.textIcon} />
        </View>
        <CalendarStrip
          onDateSelected={date => setSelectedDate(date)}
          selectedDate={selectedDate}
          scrollable
          scrollToOnSetSelectedDate
          useIsoWeekday={false}
          style={{height: 180, marginBottom: -80}}
          dateNameStyle={styles.dateName}
          dateNumberStyle={[{fontSize: 20, color: '#2C2C2E2E'}]}
          dayContainerStyle={styles.dayContainer}
          maxDayComponentSize={80}
          minDayComponentSize={65}
          dayComponentHeight={86}
          highlightDateContainerStyle={{
            backgroundColor: theme.lightColors?.primary,
          }}
          highlightDateNumberStyle={{color: theme.lightColors?.white}}
          highlightDateNameStyle={{color: theme.lightColors?.white}}
          iconLeftStyle={styles.calendarLeftIcon}
          iconRightStyle={styles.calendarRightIcon}
          calendarHeaderContainerStyle={{
            marginTop: -80,
          }}
          calendarHeaderStyle={{
            fontSize: 14,
          }}
        />

        {/* LOADING */}
        {isLoading && (
          <ActivityIndicator
            color={theme.lightColors?.primary}
            size="large"
            style={{marginTop: 20}}
          />
        )}

        {/* MEAL POSTS */}
        {!isLoading && mealPosts?.length ? (
          <View>
            {/* Meal Summary */}
            {/* <View style={styles.summaryContainer}>
              <Text
                style={{
                  marginBottom: 12,
                  fontFamily: Fonts.bold,
                  fontSize: 16,
                }}>
                Summary
              </Text>

              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>{t('Daily Target')}</Text>
                <Text style={styles.summaryValue}>{`${clientGoal?.calories} ${t(
                  'cal',
                )}`}</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>{t('Consumed')}</Text>
                <Text style={styles.summaryValue}>{`${
                  currentNutrients?.usedCalories
                } ${t('cal')}`}</Text>
              </View>

              <View style={styles.summaryItem}>
                <Text style={styles.summaryLabel}>{t('Burned')}</Text>
                <Text style={styles.summaryValue}>{`${
                  dailyActivity?.totalCaloriesBurned || 0
                } ${t('cal')}`}</Text>
              </View>
              <Divider
                style={{marginTop: 8}}
                color={theme.lightColors?.grey1}
              />

              <ProgressBarComponent
                carbsIntake={currentNutrients.usedCarbs}
                carbsTotal={clientGoal?.carbs || 0}
                fatsIntake={currentNutrients.usedFats}
                fatsTotal={clientGoal?.fat || 0}
                proteinTotal={clientGoal?.proteins || 0}
                protenIntake={currentNutrients.usedProtiens}
              />
            </View> */}
            {mealPosts.map(item => {
              return (
                <MealInfo
                  key={item._id}
                  mealPost={item}
                  clientName={item?.client?.name}
                  imageURI={item.client.image}
                  review={item.review}
                  time={formatMealTime(item?.createdAt)}
                  mealType={item.mealTitle}
                  carbAmount={item?.mealFoods
                    .reduce((totalCarbs, foodItem) => {
                      return totalCarbs + (foodItem?.carbs || 0);
                    }, 0)
                    .toString()}
                  fatAmount={item?.mealFoods
                    .reduce((totalFats, foodItem) => {
                      return totalFats + (foodItem?.fats || 0);
                    }, 0)
                    .toString()}
                  proteinAmount={item?.mealFoods
                    .reduce((totalProteins, foodItem) => {
                      return totalProteins + (foodItem?.proteins || 0);
                    }, 0)
                    .toString()}
                  totalCalories={item?.mealFoods.reduce(
                    (totalCalories, foodItem) => {
                      return totalCalories + (foodItem?.calories || 0);
                    },
                    0,
                  )}
                  mealDetails={item?.mealFoods.map(foodItem => foodItem?.name)}
                />
              );
            })}
          </View>
        ) : null}

        {/* NO DATA */}
        {!isLoading && !mealPosts?.length && (
          <Text style={styles.emptyText}>{t('No Meal in this day')}.</Text>
        )}
      </ScrollView>
    </View>
  );
};

export default ClientHistory;
