import React, {useState} from 'react';
import {ListItem} from '../../../components';
import {IUser} from '../../../interfaces/IUser.';
import GeneralModal from '../../../components/Modals/GeneralModal/GeneralModal';
import useClients from '../../../hooks/models/useClients';
import {useTranslation} from 'react-i18next';
import {StyleSheet} from 'react-native';
import {theme} from '../../../utilities/theme';

interface Props {
  client: IUser;
  onClientPress: () => void;
  onChatPress: () => void;
}

const ClientListItem: React.FC<Props> = ({
  client,
  onClientPress,
  onChatPress,
}) => {
  const [menuOpened, setMenuOpened] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [clientID, setClientId] = useState('');
  const {handleDeleteClient, handleArchiveClient} = useClients();
  const {t} = useTranslation();
  const onDeleteClient = async () => {
    setModalVisible(false);
    handleDeleteClient(clientID);
    setDeleteLoading(false);
  };

  return (
    <>
      <ListItem
        key={client._id}
        opened={menuOpened}
        setOpened={() => setMenuOpened(!menuOpened)}
        onClientPress={onClientPress}
        name={client.name}
        avatar={client.image}
        data={[
          {
            id: 1,
            label: t('Message'),
            onPress: onChatPress,
          },
          {
            id: 2,
            label: t('Archive'),
            onPress: () => {
              handleArchiveClient(client._id, true);
            },
          },
          {
            id: 3,
            label: t('Delete'),
            onPress: () => {
              setModalVisible(true);
              setClientId(client._id);
            },
            style: styles.deleteText,
          },
        ]}
      />
      <GeneralModal
        visible={isModalVisible}
        onCancel={() => setModalVisible(false)}
        primaryButtonOnpress={onDeleteClient}
        topRedTitle={t('Delete Client')}
        description={t('You are attempting to delete client.')}
        primaryButtonName={t('Yes, Delete')}
        secondaryButtonText={t('Cancel')}
        loading={deleteLoading}
        disabled={deleteLoading}
      />
    </>
  );
};

export default ClientListItem;

const styles = StyleSheet.create({
  deleteText: {
    color: theme.lightColors?.error,
  },
});
