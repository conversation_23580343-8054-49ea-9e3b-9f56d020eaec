import {Skeleton} from '@rneui/base';
import React from 'react';
import {View, StyleSheet, FlatList} from 'react-native';

const SkeletonListItem = () => (
  <View style={styles.listItem}>
    <Skeleton circle width={34} height={34} />
    <View style={styles.textContainer}>
      <Skeleton width="60%" height={16} style={styles.marginBottom} />
    </View>
    <Skeleton width={12} height={20} />
  </View>
);

const LoadingClientItem = () => {
  const skeletonData = Array(7).fill({});

  return (
    <FlatList
      data={skeletonData}
      keyExtractor={(item, index) => index.toString()}
      renderItem={() => <SkeletonListItem />}
    />
  );
};

const styles = StyleSheet.create({
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 10,
    borderColor: '#ddd',
    borderWidth: 1,
    marginBottom: 12,
    paddingVertical: 16,
  },
  textContainer: {
    flex: 1,
    marginLeft: 10,
  },
  marginBottom: {
    marginBottom: 5,
  },
});

export default LoadingClientItem;
