import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  container: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 18,
  },
  clientText: {
    fontSize: 12,
    color: '#A0A0A0',
    fontFamily: Fonts.regular,
  },
  clientText2: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
  },

  buttonContainer: {
    width: 60,
    height: 60,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 60 / 2,
    position: 'absolute',
    bottom: 17,
    right: 17,
    backgroundColor: theme.lightColors?.white,
    paddingTop: 4,
    shadowColor: theme.lightColors?.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  noClientsImage: {
    height: 120,
    width: 150,
  },
  noClientsHeading: {
    color: '#00000080',
    marginTop: 16,
    fontWeight: '600',
    fontSize: 16,
    fontFamily: Fonts.bold,
  },
  noClientsText: {
    fontSize: 12,
    color: '#00000080',
    marginTop: 8,
    textAlign: 'center',
    width: '70%',
  },
  emptyContainer: {alignItems: 'center', marginTop: 40},
  activityIndicator: {alignSelf: 'center', marginTop: 100},
});
