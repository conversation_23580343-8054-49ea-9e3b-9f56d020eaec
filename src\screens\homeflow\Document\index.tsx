import {Image, TouchableOpacity, View} from 'react-native';
import React, {useMemo, useState} from 'react';
import {SearchBar} from '../../../components';
import DocumentComponent from '../../../components/DocumentComponent/DocumentComponent';
import PNGIcons from '../../../assets/pngIcons';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {styles} from './styles';
import DocumentServices from '../../../services/DocumentServices';
import {AxiosError} from 'axios';
import {Text} from 'react-native';
import {useAppDispatch, useAppSelector} from '../../../store';
import {useTranslation} from 'react-i18next';
import {removeDocument} from '../../../store/DocumentsSlice';
import {useDebounce} from 'use-debounce';
import useApiHandler from '../../../utilities/useApiHandler';
import {ScrollView} from 'react-native-gesture-handler';
type Props = NativeStackScreenProps<HomeStackParamList, 'Document'>;

const Document: React.FC<Props> = ({navigation, route}) => {
  const dispatch = useAppDispatch();
  const documents = useAppSelector(state => state.documents.documents);
  const {t} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  const [search, setSearch] = useState('');
  const [debouncedSearch] = useDebounce(search, 800);

  const filteredDocuments = useMemo(() => {
    return documents.filter(doc =>
      doc.title.toLowerCase().includes(debouncedSearch.toLowerCase()),
    );
  }, [documents, debouncedSearch]);

  const isClient = route.params?.isClient;

  // DELETE THIS DOCUMENT
  async function deleteDocument(documentId: string) {
    try {
      dispatch(removeDocument({_id: documentId}));
      await DocumentServices.DeleteDocument(documentId);
    } catch (error) {
      const err = error as AxiosError;
      handleAxiosErrors(err);
    }
  }

  return (
    <View style={styles.container}>
      <SearchBar value={search} onChangeText={v => setSearch(v)} />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContainer}>
        {filteredDocuments.reverse().map((item, i) => {
          return (
            <DocumentComponent
              document={item}
              key={i}
              client={isClient}
              onDocumentPress={() =>
                navigation.navigate('DocumentDetail', {document: item})
              }
              onDeletePress={() => deleteDocument(item._id)}
              onEditPress={() =>
                navigation.navigate('EditDocument', {document: item})
              }
            />
          );
        })}
        {/* NO DATA */}
        {!filteredDocuments.length ? (
          <View style={styles.emptyTextContainer}>
            <Text style={styles.emptyText}>{t('No Documents Found')}</Text>
          </View>
        ) : null}
      </ScrollView>

      {!isClient && (
        <TouchableOpacity
          style={styles.addIconContainer}
          onPress={() => navigation.navigate('AddDocument')}>
          <Image source={PNGIcons.AddIcon} style={styles.addIcon} />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default Document;
