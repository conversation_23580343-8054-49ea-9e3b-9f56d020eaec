import {StyleSheet, View} from 'react-native';
import React, {useState} from 'react';
import {theme} from '../../../utilities/theme';
import {Button, FormInput} from '../../../components';
import PdfFileContainer from '../../../components/PdfFileContainer/PdfFileContainer';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import {useFormik} from 'formik';
import * as yup from 'yup';
import DocumentServices from '../../../services/DocumentServices';
import {AxiosError} from 'axios';
import Toast from 'react-native-toast-message';
import {useAppDispatch} from '../../../store';
import {updateDocument} from '../../../store/DocumentsSlice';
import {useTranslation} from 'react-i18next';

type Props = NativeStackScreenProps<HomeStackParamList, 'EditDocument'>;

interface FormData {
  title: string;
  description: string;
}

const editDocSchema = yup.object({
  title: yup.string().required('Document Name is required.'),
  description: yup.string().required('Document Description is required.'),
});

const EditDocument: React.FC<Props> = ({navigation, route}) => {
  const {document} = route.params;
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  async function handleSubmitForm(formData: FormData) {
    try {
      dispatch(
        updateDocument({
          ...document,
          _id: document._id,
          title: formData.title,
          description: formData.description,
        }),
      );
      navigation.goBack();
      await DocumentServices.UpdateDocument(document._id, formData);
    } catch (error) {
      const err = error as AxiosError;
      console.log(err.response?.data);
      // INTERNET NOT WORKING
      if (err.code === 'ERR_NETWORK') {
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t('Make sure your internet is working.'),
        });
      } else {
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t("Couldn't update the document."),
        });
      }
    }
  }

  // FORMIK
  const formik = useFormik({
    initialValues: {
      title: document.title,
      description: document.description,
    },
    onSubmit: handleSubmitForm,
    validationSchema: editDocSchema,
  });

  return (
    <View style={styles.container}>
      <View>
        <FormInput
          label={t('Name')}
          value={formik.values.title}
          onChangeText={formik.handleChange('title')}
          errorMessage={
            formik.touched.title ? t(formik.errors.title as string) : undefined
          }
          labelStyle={{marginTop: 34}}
        />
        <FormInput
          label={t('Description')}
          value={formik.values.description}
          onChangeText={formik.handleChange('description')}
          textinputStyles={{paddingVertical: 10}}
          errorMessage={
            formik.touched.description
              ? t(formik.errors.description as string)
              : undefined
          }
          multiline
        />
        <PdfFileContainer fileName={document.title} fileUrl={document.docUrl} />
      </View>
      <Button
        title={t('Save')}
        containerStyle={{marginBottom: 40}}
        onPress={formik.handleSubmit}
      />
    </View>
  );
};

export default EditDocument;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
});
