import {KeyboardAvoidingView, Platform, StyleSheet, View} from 'react-native';
import React from 'react';
import {theme} from '../../../utilities/theme';
import {Button, FormInput} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import {useTranslation} from 'react-i18next';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
type Props = NativeStackScreenProps<HomeStackParamList, 'HelpCenter'>;
const HelpCenter: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();
  const handleSubmit = () => {
    // TODO: HANDLE SUBMIT
    console.log('SUBMIT help center needs to be implemented');
    navigation.goBack();
  };
  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        enableAutomaticScroll={true}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}>
        <View style={styles.formContainer}>
          <FormInput label={t('Name')} labelStyle={{marginTop: 41}} />
          <FormInput label={t('Subject')} />
          <FormInput
            label={t('Your message')}
            textinputStyles={{height: 183}}
            multiline
            textAlignVertical="top"
          />
        </View>
        <View style={styles.buttonContainer}>
          <Button
            title={t('Send')}
            onPress={handleSubmit}
            containerStyle={{marginBottom: 40}}
          />
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

export default HelpCenter;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
  },
  formContainer: {
    flex: 1,
  },
  buttonContainer: {
    paddingTop: 20,
    paddingBottom: 20,
  },
});
