import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {ListItem} from '@rneui/base';
import {Fonts, theme} from '../../../utilities/theme';
import {ActiveBell, InActiveBell} from '../../../assets/svgIcons';
import {useTranslation} from 'react-i18next';

interface Props {
  isActive: boolean;
}

const NotificationItem: React.FC<Props> = ({isActive}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      {isActive ? (
        <View style={styles.dividerContainer}>
          <Text style={styles.timeText}>{t('Nov 21')}</Text>
          <View style={styles.divider} />
        </View>
      ) : null}
      <TouchableOpacity>
        <ListItem
          containerStyle={[
            styles.innerContainer,
            {
              backgroundColor: isActive
                ? `${theme.lightColors?.primary}10`
                : undefined,
            },
            {
              borderColor: isActive
                ? theme.lightColors?.primary
                : theme.lightColors?.grey1,
            },
          ]}>
          <View
            style={[
              styles.iconContainer,
              {
                backgroundColor: isActive
                  ? theme.lightColors?.primary
                  : theme.lightColors?.grey1,
              },
            ]}>
            {isActive ? (
              <ActiveBell width={25} height={25} />
            ) : (
              <InActiveBell width={25} height={25} />
            )}
          </View>
          <ListItem.Content style={styles.titleContainer}>
            <ListItem.Title style={styles.messageType}>
              {t('New message!')}
            </ListItem.Title>
            <ListItem.Title style={styles.message}>
              {t("You've a new message.")}
            </ListItem.Title>
          </ListItem.Content>
        </ListItem>
      </TouchableOpacity>
    </View>
  );
};

export default NotificationItem;

const styles = StyleSheet.create({
  container: {},
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  divider: {
    height: 1,
    backgroundColor: '#BBBBBB',
    width: '85%',
  },
  timeText: {
    fontSize: 12,
    color: '#BBBBBB',
    fontFamily: Fonts.regular,
  },
  innerContainer: {
    paddingVertical: 12,
    paddingHorizontal: 0,
    marginTop: 14,
    paddingLeft: 8,
    borderRadius: 14,
    borderWidth: 1,
  },
  messageType: {
    fontSize: 16,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
  },
  message: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}60`,
    paddingTop: 2,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,

    alignItems: 'center',
    justifyContent: 'center',
  },
  titleContainer: {marginLeft: 0, paddingLeft: 0},
});
