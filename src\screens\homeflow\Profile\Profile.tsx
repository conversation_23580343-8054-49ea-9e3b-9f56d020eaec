import {SafeAreaView, Text, View} from 'react-native';
import React, {useState} from 'react';
import {Avatar} from '../../../components';
import {theme} from '../../../utilities/theme';
import {About, Barcode, Logout, VerifiedIcon} from '../../../assets/svgIcons';
import TextButtonwithIcon from '../../../components/ButtonwithIcon/ButtonwithIcon';
import {Divider} from '@rneui/base';
import ProfileAction from './ProfileAction';
import {
  Lock,
  Profile_,
  Documents,
  Language,
  Terms,
  Help,
} from '../../../assets/svgIcons/ProfileActionIcons';
import {ScrollView} from 'react-native';
import type {NativeStackScreenProps} from '@react-navigation/native-stack';
import {BottomStackParamList} from '../../../navigation/BottomNavigation/BottomNavigation';
import {useAppDispatch, useAppSelector} from '../../../store';
import {resetUser, updateUser} from '../../../store/userSlice';
import GeneralModal from '../../../components/Modals/GeneralModal/GeneralModal';
import {styles} from './styles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ImagePicker from 'react-native-image-crop-picker';
import AuthServices from '../../../services/AuthServices';
import {AxiosError} from 'axios';
import {IUser} from '../../../interfaces/IUser.';
import {useTranslation} from 'react-i18next';
import {resetStats} from '../../../store/StatisticsSlice';
import {resetMeals} from '../../../store/MealsSlice';
import {resetMealPosts} from '../../../store/mealPostSlice';
import {resetFoods} from '../../../store/FoodsSlice';
import {resetDocuments} from '../../../store/DocumentsSlice';
import {resetClients} from '../../../store/clientsSlice';
import {resetChats} from '../../../store/chatsSlice';

type Props = NativeStackScreenProps<BottomStackParamList, 'Profile'>;
const Services = new AuthServices();
const Profile: React.FC<Props> = ({navigation}) => {
  const user = useAppSelector(state => state.user.user);
  const {t} = useTranslation();

  const [isModalVisible, setModalVisible] = useState(false);
  const dispatch = useAppDispatch();
  const userData = useAppSelector(state => state.user.user);
  const [selectedImage, setSelectedImage] = useState(userData.photo);

  const handleLogout = async () => {
    try {
      dispatch(resetUser());
      dispatch(resetStats());
      dispatch(resetMeals());
      dispatch(resetMealPosts());
      dispatch(resetFoods());
      dispatch(resetDocuments());
      dispatch(resetClients());
      dispatch(resetChats());
      setModalVisible(false);
      await AsyncStorage.removeItem('userData');
      await AsyncStorage.removeItem('access_token');
    } catch (error) {
      console.log('Error logging out', error);
    }
  };

  const handleImagePress = async () => {
    console.log('Image Pressed');
    try {
      const image = await ImagePicker.openPicker({
        width: 300,
        height: 400,
        cropping: false,
        mediaType: 'photo',
      });
      setSelectedImage(image.path);
      const formData = new FormData();
      formData.append('file', {
        uri: image.path,
        type: image.mime,
        name: userData._id,
      });

      const resp = await Services.UploadPicture(formData);
      console.log('response ,', resp);

      if (resp.status === 201) {
        dispatch(updateUser({photo: resp.data.url} as IUser));
      }
    } catch (error) {
      const err = error as AxiosError;
      console.log('Error selecting image', err.response);
    }
  };

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        {/* AVATAR */}
        <Avatar uri={user.photo} onPress={handleImagePress} showPickImage />
        {/* ROW CONTAINER */}
        <View style={styles.rowContainer}>
          <View style={{width: 24}} />
          <View style={styles.rowContainer}>
            <Text style={styles.name}>{userData.name} </Text>
            <VerifiedIcon width={16} height={16} />
          </View>
          <TextButtonwithIcon
            leftIcon={<Barcode />}
            LeftIconContainer={styles.iconContainer}
            containerStyle={styles.containerStyles}
            onPress={() => navigation.navigate('ShareBarcode')}
          />
        </View>
        {/* DIVIDER */}
        <Divider style={styles.divider} />
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{paddingBottom: 16}}>
          {/* PROFILE ACTION */}
          <ProfileAction
            icon={<Profile_ />}
            label={t('Personal Information')}
            title={t('General')}
            marginTop={0.1}
            onPress={() => navigation.navigate('PersonalInfo')}
          />
          {/* <ProfileAction
            icon={<Subscribtion />}
            label={t('Subscription')}
            onPress={() =>
              navigation.navigate('ViewSubscription', {isProfile: true})
            }
          /> */}
          <ProfileAction
            icon={<Documents />}
            label={t('Document')}
            onPress={() => navigation.navigate('Document')}
          />
          <ProfileAction
            icon={<Lock />}
            label={t('Change Password')}
            onPress={() => navigation.navigate('PreviousPassword')}
          />
          <ProfileAction
            icon={<Language />}
            label={t('Language')}
            onPress={() => navigation.navigate('Language2', {isClient: true})}
          />
          <ProfileAction
            icon={<Terms />}
            label={t('Terms & Conditions')}
            title={t('Help & Support')}
            onPress={() => navigation.navigate('TermsAndCondition')}
          />
          <ProfileAction
            icon={<Help />}
            label={t('Help Center')}
            onPress={() => navigation.navigate('HelpCenter')}
          />
          <ProfileAction
            icon={
              <About
                width={28}
                height={28}
                stroke={theme.lightColors?.primary}
              />
            }
            label={t('About')}
            onPress={() => navigation.navigate('About')}
          />
          <ProfileAction
            icon={
              <Logout
                width={28}
                height={28}
                stroke={theme.lightColors?.primary}
              />
            }
            label={t('Log Out')}
            title={t('Log Out')}
            onPress={() => setModalVisible(true)}
          />
        </ScrollView>
      </View>
      <GeneralModal
        visible={isModalVisible}
        onCancel={() => setModalVisible(false)}
        primaryButtonOnpress={handleLogout}
        topRedTitle={t('Log Out')}
        description={t('You are attempting to log out.')}
        primaryButtonName={t('Yes, Log Out')}
        secondaryButtonText={t('Cancel')}
      />
    </SafeAreaView>
  );
};

export default Profile;
