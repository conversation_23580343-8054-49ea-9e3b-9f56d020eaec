import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {ListItem} from '@rneui/themed';
import {Fonts, theme} from '../../../utilities/theme';
import {ChevronRight} from '../../../assets/svgIcons';

interface Props {
  icon: React.ReactNode;
  title?: string;
  label: string;
  marginTop?: number;
  onPress?: () => void;
}

const ProfileAction: React.FC<Props> = ({
  icon,
  title,
  label,
  marginTop,
  onPress,
}) => {
  return (
    <View>
      {title ? (
        <Text style={[styles.title, {marginTop: marginTop ? marginTop : 16}]}>
          {title}
        </Text>
      ) : undefined}
      <TouchableOpacity onPress={onPress}>
        <ListItem
          containerStyle={[
            styles.innerContainer,
            {marginTop: title ? 12 : 14},
          ]}>
          <View style={styles.iconContainer}>{icon}</View>
          <ListItem.Content style={styles.labelItem}>
            <ListItem.Title style={styles.label}>{label}</ListItem.Title>
          </ListItem.Content>
          <ChevronRight />
        </ListItem>
      </TouchableOpacity>
    </View>
  );
};

export default ProfileAction;

const styles = StyleSheet.create({
  title: {
    fontSize: 16,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.bold,
  },
  innerContainer: {
    paddingHorizontal: 0,
    paddingVertical: 0,
    backgroundColor: theme.lightColors?.white,
  },
  label: {
    fontSize: 16,
    color: '#707070',
    fontFamily: Fonts.regular,
    marginLeft: 0,
    paddingLeft: 0,
    textTransform: 'capitalize',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: `${theme.lightColors?.grey1}`,
    alignItems: 'center',
    justifyContent: 'center',
  },
  labelItem: {marginLeft: 0, paddingLeft: 0},
});
