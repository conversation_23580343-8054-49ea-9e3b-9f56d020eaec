import {
  Share,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {Button} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import QRCode from 'react-native-qrcode-svg';
import {useAppSelector} from '../../../store';
import {useTranslation} from 'react-i18next';
import Clipboard from '@react-native-clipboard/clipboard';
import Toast from 'react-native-toast-message';

// Polyfill for TextEncoder if not available
if (typeof global.TextEncoder === 'undefined') {
  global.TextEncoder = require('text-encoding').TextEncoder;
}
if (typeof global.TextDecoder === 'undefined') {
  global.TextDecoder = require('text-encoding').TextDecoder;
}

type Props = NativeStackScreenProps<HomeStackParamList, 'ShareBarcode'>;
const ShareBarcode: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();
  const userData = useAppSelector(state => state.user.user);

  const handleCopyCode = () => {
    Clipboard.setString(userData.referralCode || '');
    Toast.show({
      type: 'success',
      text1: t('Copied'),
      text2: t('Referral code copied to clipboard'),
    });
  };

  const handleQRCodePress = () => {
    handleCopyCode();
  };

  return (
    <View style={styles.container}>
      <View style={{alignItems: 'center'}}>
        <Text style={[styles.QRText, styles.QRMargins]}>{t('QR')}</Text>
        <TouchableOpacity onPress={handleQRCodePress} activeOpacity={0.7}>
          <QRCode
            value={userData.referralCode}
            size={105}
            backgroundColor="white"
            color="black"
          />
        </TouchableOpacity>
        <Text style={[styles.QRText, {marginTop: 82}]}>{t('Code')}</Text>
        <TouchableOpacity
          style={styles.inviteCodeContainer}
          onPress={handleCopyCode}
          activeOpacity={0.4}>
          <Text style={[styles.QRText, {textAlign: 'center'}]}>
            {userData.referralCode}
          </Text>
        </TouchableOpacity>
      </View>

      <Button
        title={t('Share')}
        onPress={() => Share.share({message: userData.referralCode || ''})}
        containerStyle={{marginBottom: 40}}
      />
    </View>
  );
};

export default ShareBarcode;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  QRText: {
    fontSize: 14,
    fontFamily: Fonts.bold,
    lineHeight: 19,
    color: 'black',
  },
  QRImage: {height: 104, width: 104, marginTop: 39},
  inviteCodeContainer: {
    paddingVertical: 14,
    marginTop: 22,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 10,
    backgroundColor: '#FAFAFA',
    width: '100%',
  },
  copyHint: {
    fontSize: 12,
    fontFamily: Fonts.regular,
    color: theme.lightColors?.grey4,
  },
  QRMargins: {marginTop: 60, marginBottom: 32},
});
