import {
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import images from '../../../assets/images';
import {TermsIcon} from '../../../assets/svgIcons';
import {useTranslation} from 'react-i18next';

const TermsAndCondition = () => {
  const {t} = useTranslation();

  return (
    <SafeAreaView style={styles.screenWrapper}>
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.logo}>
            {/* LOGO */}
            <TermsIcon width={100} height={100} />
          </View>
          {/* PARAGRPH */}
          <Text style={styles.paragraph}>
            {t(
              'By accessing or using our nutrition app Food Log, you agree to comply with and be bound by the following terms and conditions\n\nPlease read these terms carefully. If you do not agree with these terms, you should not use the app and contact us.\n\n You agree to use the App for lawful purposes only.\n\n You must be at least 18 years old or have parental consent to use the app.\n\n To use certain features of the App, you may be required to create an account. You agree to provide accurate and complete information during the registration process.\n\n You are responsible for maintaining the confidentiality of your account information and for all activities that occur under your account.\n\n The App may provide health and nutrition information, including diet recommendations, meal plans, and fitness advice. However, this information is not intended as medical advice. Always consult with a healthcare provider before making any changes to your diet or exercise routine.\n\n The App does not guarantee the accuracy, completeness, or reliability of the information provided.\n\n Your use of the App is subject to our Privacy Policy, which outlines how we collect, use, and protect your personal data.',
            )}
          </Text>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

export default TermsAndCondition;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingBottom: 16,
    paddingHorizontal: 24,
  },
  paragraph: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.medium,
    paddingTop: 20,
  },
  screenWrapper: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
  },
  containerStyle: {
    marginLeft: 0,
    marginTop: 56,
    paddingVertical: 0,
    paddingLeft: 0,
  },
  textStyle: {
    fontSize: 12,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.light,
    marginLeft: 10,
    fontWeight: '400',
  },
  logo: {alignItems: 'center'},
});
