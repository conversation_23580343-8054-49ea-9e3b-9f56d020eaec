import {ScrollView, Text, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {Button} from '../../../components';
import {CheckCircle, RightArrow} from '../../../assets/svgIcons';
import {theme} from '../../../utilities/theme';
import {ListItem} from '@rneui/themed';
import {SUBSCRIPTIONS} from '../../../constants/Subscription';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import GeneralModal from '../../../components/Modals/GeneralModal/GeneralModal';
import {styles} from './styles';
import {useTranslation} from 'react-i18next';

type Props = NativeStackScreenProps<HomeStackParamList, 'ViewSubscription'>;

const ViewSubscription: React.FC<Props> = ({navigation}) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const {t} = useTranslation();

  return (
    <View style={[styles.container]}>
      <ScrollView
        contentContainerStyle={{justifyContent: 'space-between', flex: 1}}>
        <View style={[styles.innerContainer]}>
          <Text style={styles.packageTypeText}>{t('Basic')}</Text>
          <Text style={styles.largeTitle}>
            {t('$900/')}
            <Text style={styles.largeTitle2}>{t('Month')}</Text>
          </Text>
          <Text style={styles.planIncludeText}>{t('Plan include')}</Text>
          {SUBSCRIPTIONS[0].listItem.map((_item, index) => (
            <View key={index}>
              <ListItem
                containerStyle={{
                  marginLeft: 12,
                  paddingVertical: 12,
                  backgroundColor: theme.lightColors?.background,
                }}>
                <CheckCircle />
                <ListItem.Content>
                  <ListItem.Title style={styles.listItemTitle}>
                    {_item}
                  </ListItem.Title>
                </ListItem.Content>
              </ListItem>
            </View>
          ))}
          <Text style={styles.valditText}>{t('Valid for 1 month')}</Text>
          <Button
            title={t('Update plan')}
            containerStyle={{marginHorizontal: 45}}
            onPress={() => navigation.navigate('AddNewCard', {isProfile: true})}
            rightIcon={<RightArrow stroke={theme.lightColors?.white} />}
            rightIconContainer={{paddingLeft: 6}}
          />
        </View>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => setModalVisible(true)}>
            <Text style={styles.cancelText}>{t('Cancel')}</Text>
          </TouchableOpacity>
          <Button
            title={t('Buy New')}
            containerStyle={styles.buyButtonContainer}
            onPress={() =>
              navigation.navigate('Subscription2', {isProfile: true})
            }
          />
        </View>
      </ScrollView>
      <GeneralModal
        visible={isModalVisible}
        topRedTitle={t('Cancel Subscription')}
        description={t('You are attempting to cancel your subscription.')}
        primaryButtonName={t('Yes, Cancel')}
        primaryButtonOnpress={() => setModalVisible(false)}
        secondaryButtonText={t('No')}
        onCancel={() => setModalVisible(false)}
      />
    </View>
  );
};

export default ViewSubscription;
