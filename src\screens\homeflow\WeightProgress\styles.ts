import {StyleSheet} from 'react-native';
import {Fonts, theme} from '../../../utilities/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
  },
  divider: {
    borderTopColor: theme.lightColors?.grey2,
    borderTopWidth: 1,
    marginVertical: 18,
    alignSelf: 'center',
    width: '100%',
  },
  modalHeading: {
    marginTop: 20,
    textAlign: 'center',
    fontFamily: Fonts.semiBold,
    fontSize: 16,
  },
  modalContainer: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },

  saveButton: {width: '90%', marginLeft: 13},
  unitContainer: {
    marginRight: 12,
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 5,
  },
  uploadContainer: {
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    width: '100%',
    height: 127,
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 10,
  },
  uploadText: {
    fontFamily: Fonts.regular,
    color: theme.lightColors?.secondary,
    marginTop: 24,
  },
  units: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedImage: {
    width: '100%',
    height: 127,
    borderRadius: 14,
  },
  deleteFood: {fontFamily: Fonts.semiBold, fontSize: 16},
  deleteFoodContainer: {paddingVertical: 11, paddingHorizontal: 27},
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 40,
    marginTop: 20,
  },
  recordText: {fontSize: 16, fontFamily: Fonts.semiBold},
  modalStyle: {margin: 0, justifyContent: 'flex-end'},
  uploadIcon: {width: 44, height: 47},
  scrollContainer: {paddingBottom: 40},
});
