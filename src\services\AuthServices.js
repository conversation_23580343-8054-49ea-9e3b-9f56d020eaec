import AppInstance from '../config/global.axios';

export default class AuthServices {
  /* --- COACH AUTH ---*/
  // COACH SIGN UP
  async CoachSignUp(user) {
    const response = await AppInstance({
      url: '/coach/signup',
      method: 'POST',
      data: user,
    });
    return response;
  }

  async SignIn(user) {
    return AppInstance({
      url: '/auth/signin',
      method: 'POST',
      data: user,
    });
  }

  // COACH SIGN IN
  async CoachSignIn(user) {
    const response = await AppInstance({
      url: '/coach/signin',
      method: 'POST',
      data: user,
    });
    return response;
  }
  async CoachUpdate(user, userId) {
    const response = await AppInstance({
      url: `/coach/${userId}`,
      method: 'PATCH',
      data: user,
    });
    return response;
  }

  async UpdateClient(user, userId) {
    const response = await AppInstance({
      url: `/client/${userId}`,
      method: 'PATCH',
      data: user,
    });
    return response;
  }
  async CoachGetMe(user) {
    const response = await AppInstance({
      url: '/coach/me',
      method: 'GET',
      data: user,
    });
    return response;
  }
  async ForgotPassword(user) {
    const response = await AppInstance({
      url: '/auth/forgot-password',
      method: 'POST',
      data: user,
    });
    return response;
  }
  async VerifyOtp(user) {
    const response = await AppInstance({
      url: '/auth/verify-otp',
      method: 'POST',
      data: user,
    });
    return response;
  }
  async ResetPassword(user) {
    const response = await AppInstance({
      url: '/auth/reset-password',
      method: 'POST',
      data: user,
    });
    return response;
  }
  async ChangePassword(user) {
    const response = await AppInstance({
      url: '/auth/change-password',
      method: 'POST',
      data: user,
    });
    return response;
  }
  async UploadPicture(user) {
    const response = await AppInstance({
      url: '/coach/upload-image',
      method: 'POST',
      data: user,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  }

  /* --- CLIENT AUTH ---*/

  // CLIENT SIGN UP
  async ClientSignUp(user) {
    const response = await AppInstance({
      url: '/client/signup',
      method: 'POST',
      data: user,
    });
    return response;
  }

  // CLIENT SIGN IN
  async ClientSignIn(user) {
    const response = await AppInstance({
      url: '/client/signin',
      method: 'POST',
      data: user,
    });
    return response;
  }
  async ClientUploadPicture(user) {
    const response = await AppInstance({
      url: '/client/upload-photos',
      method: 'POST',
      data: user,
    });
    return response;
  }
  async ClientGetMe(user) {
    const response = await AppInstance({
      url: '/client/me',
      method: 'GET',
      data: user,
    });
    return response;
  }
}
