import AppInstance from '../config/global.axios';

async function GetUserList(
  userId: string,
  pageSize: number,
  pageNumber: number,
) {
  return await AppInstance({
    url: `/chat/users-list?userId=${userId}&page=${pageNumber}&pageSize=${pageSize}`,
    method: 'GET',
  });
}

async function GetChats(limit?: number) {
  return await AppInstance({
    url: `/chats/get-chats`,
    method: 'GET',
  });
}

async function GetMessages(
  chatId: string,
  pageSize?: number,
  pageNumber?: number,
) {
  return await AppInstance({
    url: `/chats/${chatId}/messages`,
    method: 'GET',
  });
}

async function SendMessage(payload: { receiverId: string; message: string }) {
  return await AppInstance({
    url: `/chats/send-message`,
    method: 'POST',
    data: payload,
  });
}

async function UpdateMessage(msgId: string, payload: any) {
  const response = await AppInstance({
    url: `/message/${msgId}`,
    method: "PATCH",
    data: payload
  });
  return response;
}

const ChatServices = {
  GetUserList,
  GetChats,
  GetMessages,
  SendMessage,
  UpdateMessage
};
export default ChatServices;
