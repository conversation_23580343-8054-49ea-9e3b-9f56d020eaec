import {replace} from 'formik';
import AppInstance from '../config/global.axios';

// CLIENT SIGN UP
async function ClientSignUp(user: any) {
  const response = await AppInstance({
    url: '/client/signup',
    method: 'POST',
    data: user,
  });
  return response;
}

// CLIENT SIGN IN
async function ClientSignIn(user: any) {
  const response = await AppInstance({
    url: '/client/signin',
    method: 'POST',
    data: user,
  });
  return response;
}
async function ClientUploadPicture(user: any) {
  const response = await AppInstance({
    url: '/client/upload-photos',
    method: 'POST',
    data: user,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response;
}
async function ClientProfilePicture(user: any) {
  const response = await AppInstance({
    url: '/client/upload-image',
    method: 'POST',
    data: user,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response;
}
async function ClientGetMe(user: any) {
  const response = await AppInstance({
    url: '/client/me',
    method: 'GET',
    data: user,
  });
  return response;
}

//GET CLIENT BY ID
async function GetClientById(id: string) {
  const response = await AppInstance({
    url: `/client/${id}`,
    method: 'GET',
  });
  return response;
}

//CHANGE PASSWORD
async function ChangePassword(payload: any) {
  const response = await AppInstance({
    url: `/auth/change-password`,
    method: 'POST',
    data: payload,
  });
  return response;
}

//UPDATE CLIENT
async function UpdateClient(clientId: string, client: any) {
  const response = await AppInstance({
    url: `/client/${clientId}`,
    method: 'PATCH',
    data: client,
  });
  return response;
}

const ClientAuthServices = {
  ClientSignUp,
  ClientSignIn,
  ClientGetMe,
  ClientUploadPicture,
  ClientProfilePicture,
  GetClientById,
  ChangePassword,
  UpdateClient,
};

export default ClientAuthServices;
