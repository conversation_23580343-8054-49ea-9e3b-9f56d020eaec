import moment from 'moment';
import AppInstance from '../config/global.axios';
import { IQuestionPayload } from '../interfaces/IQuestion';

async function FetchSortedClients(
  userId: string,
  searchKey: string,
  sortType: string,
) {
  return await AppInstance({
    url: `/client/coach/${userId}?searchKey=${searchKey}&sort=${sortType}`,
    method: 'GET',
  });
}

async function DeleteClient(clientId: string) {
  return await AppInstance({
    url: `/client/${clientId}`,
    method: 'DELETE',
  });
}

async function GetGoalByDate(id: string) {
  const response = await AppInstance({
    url: `/client/goal-by-date/${id}?date=${moment().format()}`,
    method: 'GET',
  });
  return response;
}

export interface IClientGoal {
  calories: number;
  proteins: number;
  proteinsPercentage: number;
  carbs: number;
  carbsPercentage: number;
  fat: number;
  fatPercentage: number;
  client: string;
}

async function SetClientGoal(payload: IClientGoal) {
  const response = await AppInstance({
    url: `/goal`,
    method: 'POST',
    data: payload,
  });
  return response;
}

async function UpdateClientGoal(goalId: string, payload: Partial<IClientGoal>) {
  const response = await AppInstance({
    url: `/goal/${goalId}`,
    method: 'PATCH',
    data: payload,
  });
  return response;
}

async function GetArchivedClient(coachId: string) {
  return await AppInstance({
    url: `/client/archived-client/${coachId}`,
    method: 'GET',
  });
}

async function ArchiveClient(clientId: String, archive: boolean) {
  return await AppInstance({
    url: `/client/${clientId}`,
    method: 'PATCH',
    data: {
      archived: archive,
    },
  });
}

async function AddQuestion(clientId: string, question: IQuestionPayload) {
  const response = await AppInstance({
    url: `/client/add-details/${clientId}`,
    method: 'POST',
    data: { ...question, questionType: 'choose-one' },
  });
  return response;
}

interface IUpdateClient {
  age?: string;
  dob?: string;
  height?: string;
  weight?: string;
  goal?: 'Lose Weight' | 'Maintain Weight' | 'Gain Weight' | null;
  howActive?:
  | 'Sedentary'
  | 'Lightly Active'
  | 'Moderately Active'
  | 'Highly Active'
  | null;
  gender?: string;
  step?: number;
}

async function UpdateClient(clientId: string, payload: IUpdateClient) {
  const response = await AppInstance({
    url: `/client/${clientId}`,
    method: 'PATCH',
    data: payload,
  });
  return response;
}

async function UploadMultiImage(images: any) {
  const response = await AppInstance({
    url: '/client/upload-photos',
    method: 'POST',
    data: images,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response;
}

async function GetClientsOfCoach(coachId: string) {
  const response = await AppInstance({
    url: `/client/coach/${coachId}`,
    method: 'GET',
  });
  return response;
}

export interface IFiltersQueryParams {
  searchKey?: string;
  sortOrder?: 'asc' | 'desc';
  sortKey: string;
}
async function FetchCoachClients(
  coachId: string,
  filters?: IFiltersQueryParams,
) {
  // console.log('fileters----- ', filters);

  let url = `/client/coach/${coachId}`;
  if (filters) {
    const queryParams = new URLSearchParams();

    if (filters?.searchKey) {
      queryParams.append('searchKey', filters.searchKey);
    }
    if (filters?.sortOrder) {
      queryParams.append('sortOrder', filters.sortOrder);
    }
    if (filters?.sortKey) {
      queryParams.append('sortKey', filters.sortKey);
    }

    const queryString = queryParams.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
  }

  const response = await AppInstance({
    url,
    method: 'GET',
  });
  return response;
}

const ClientServices = {
  FetchCoachClients,
  FetchSortedClients,
  DeleteClient,
  GetArchivedClient,
  ArchiveClient,
  AddQuestion,
  UpdateClient,
  UploadMultiImage,
  GetGoalByDate,
  SetClientGoal,
  GetClientsOfCoach,
  UpdateClientGoal
};
export default ClientServices;
