import AppInstance from '../config/global.axios';

async function UploadDocument(data: any) {
  return await AppInstance({
    url: '/document/upload-doc',
    method: 'POST',
    data: data,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
}

async function CreateDocument(
  coachId: string,
  payload: {
    title: string,
    description: string,
    docUrl: string,
  }
) {
  return await AppInstance({
    url: `/document`,
    method: 'POST',
    data: payload
  });
}

async function UpdateDocument(
  documentId: string,
  payload: {
    title?: string,
    description?: string,
    docUrl?: string,
  }
) {
  return await AppInstance({
    url: `/document/${documentId}`,
    method: 'PATCH',
    data: payload
  });
}

async function fetchDocuments() {
  return await AppInstance({
    url: `/document`,
    method: 'GET',
  });
}

async function DeleteDocument(documentId: string) {
  const response = await AppInstance({
    url: `/document/${documentId}`,
    method: "DELETE"
  });
  return response;
}

async function GetDocumentOfCoach(coachId: string) {
  const response = await AppInstance({
    url: `/document/coach/${coachId}`,
    method: "GET"
  });
  return response;
}

const DocumentServices = {
  UploadDocument,
  CreateDocument,
  UpdateDocument,
  fetchDocuments,
  DeleteDocument,
  GetDocumentOfCoach
};
export default DocumentServices;
