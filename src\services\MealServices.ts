import AppInstance from '../config/global.axios';
import {IMeal, MealPayload} from '../interfaces/IMeal';

interface ReviewPayload {
  text?: string;
  isReacted?: boolean;
  coach: string;
  client: string;
  meal: string;
}

async function CreateMeal(meal: any) {
  const response = await AppInstance({
    url: '/meal',
    method: 'POST',
    data: meal,
  });
  return response;
}

async function GetMealsByClient(clientId: string, date?: string) {
  const url = date
    ? `/meal/client/${clientId}?date=${date}`
    : `/meal/client/${clientId}`;

  const response = await AppInstance({
    url,
    method: 'GET',
  });
  return response;
}

async function GetMealDetails(mealId: string) {
  const response = await AppInstance({
    url: `/meal/${mealId}`,
    method: 'GET',
  });
  return response;
}

async function UpdateMeal(mealId: string, mealData: Partial<MealPayload>) {
  const response = await AppInstance({
    url: `/meal/${mealId}`,
    method: 'PATCH',
    data: mealData,
  });
  return response;
}

async function GetMealPostsForCoach(coachId: string) {
  const response = await AppInstance({
    url: `/meal/coach/${coachId}`,
    method: 'GET',
  });
  return response;
}

// COACH ADDS A REVIEW WHEN CLIENT EATS A MEAL
async function AddReviewForMeal(reviewFoodPayload: ReviewPayload) {
  const response = await AppInstance({
    url: '/review',
    method: 'POST',
    data: reviewFoodPayload,
  });
  return response;
}

// COACH UPDATES A REVIEW ON A MEAL
async function UpdateReviewForMeal(
  reviewId: string,
  reviewFoodPayload: Partial<ReviewPayload>,
) {
  const response = await AppInstance({
    url: `/review/${reviewId}`,
    method: 'PATCH',
    data: reviewFoodPayload,
  });
  return response;
}

// DELETE REVIEW ON A MEAL
async function DeleteReviewOnMeal(reviewId: string) {
  const response = await AppInstance({
    url: `/review/${reviewId}`,
    method: 'DELETE',
  });
  return response;
}

const MealServices = {
  CreateMeal,
  GetMealsByClient,
  GetMealDetails,
  UpdateMeal,
  GetMealPostsForCoach,
  AddReviewForMeal,
  UpdateReviewForMeal,
  DeleteReviewOnMeal,
};

export default MealServices;
