import AppInstance from "../config/global.axios";

// ADD WATER TO CLIENT
async function AddWater(water: any) {
  const response = await AppInstance({
    url: "/water-intake",
    method: "POST",
    data: water
  });
  return response;
}

// GET WATER DRUNK BY CLIENT
async function GetWaterByClient(clientId: string) {
  const response = await AppInstance({
    url: `/water-intake/client/${clientId}`,
    method: "GET",
  });
  return response;
}

// GET WATER DRUNK BY CLIENT FOR SPECIFIC DATE
async function GetWaterByDate(date: string) {
  const response = await AppInstance({
    url: `/water-intake/get/date?date=${date}`,
    method: "GET",
  });
  return response;
}

// UPDATE WATER DRUNK BY CLIENT
async function UpdateWater(waterId: string, water: any) {
  const response = await AppInstance({
    url: `/water-intake/${waterId}`,
    method: "PATCH",
    data: water
  });
  return response;
}

const WaterService = {
  AddWater,
  GetWaterByClient,
  GetWaterByDate,
  UpdateWater
}

export default WaterService;