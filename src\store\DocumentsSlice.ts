import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {IDocument} from '../interfaces/IDocument';

interface IDocumentInitialState {
  documents: IDocument[];
}

const initialState: IDocumentInitialState = {
  documents: [],
};

// SLICE
export const DocumentsSlice = createSlice({
  name: 'documents',
  initialState,
  reducers: {
    setDocuments: (state, {payload}: PayloadAction<Array<IDocument>>) => {
      state.documents = payload;
      return state;
    },
    addNewDocument: (state, {payload}: PayloadAction<IDocument>) => {
      state.documents = [...state.documents, payload];
      return state;
    },
    updateDocument: (state, {payload}: PayloadAction<Partial<IDocument>>) => {
      let index = state.documents.findIndex(item => item._id === payload._id);
      state.documents[index] = {...state.documents[index], ...payload};
      return state;
    },
    removeDocument: (state, {payload}: PayloadAction<Partial<IDocument>>) => {
      state.documents = state.documents.filter(
        item => item._id !== payload._id,
      );
      return state;
    },
    resetDocuments: state => {
      state = initialState;
      return state;
    },
  },
});

// ACTIONS
export const {
  setDocuments,
  addNewDocument,
  updateDocument,
  removeDocument,
  resetDocuments,
} = DocumentsSlice.actions;

// REDUCER
export default DocumentsSlice;
