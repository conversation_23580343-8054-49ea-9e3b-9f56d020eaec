// src/store/languageSlice.ts
import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n from '../i18n/i18n';
// import moment from 'moment';
// import enLocale from 'moment/';
// import esLocale from 'moment/locale/es';
// import 'moment/locale/ar';
// import 'moment/locale/fr';
// import 'moment/locale/ko';
// import 'moment/locale/pt';

export type LanguageInitial = 'en' | 'en-GB' | 'es' | 'ar' | 'fr' | 'ko' | 'pt';

interface LanguageState {
  selectedLanguage: LanguageInitial;
}

const initialState: LanguageState = {
  selectedLanguage: 'en', // Default language set to 'en'
};

// function setMomentLocale(lang: LanguageInitial) {
//   switch (lang) {
//     case "en":
//       moment.updateLocale('en', enLocale);
//       break;
//     case "en-GB":
//       moment.locale('en');
//       break;
//     case "ar":
//       moment.locale('ar');
//       break;
//     case "es":
//       moment.updateLocale('es', esLocale);
//       break;
//     case "fr":
//       moment.locale('fr');
//       break;
//     case "ko":
//       moment.locale('ko');
//       break;
//     case "pt":
//       moment.locale('pt');
//       break;
//     default:
//       moment.locale('en');
//       break;
//   }
// }

// Async thunk to load the language from AsyncStorage
export const loadLanguage = createAsyncThunk(
  'language/loadLanguage',
  async (): Promise<LanguageInitial> => {
    try {
      const storedLanguage = await AsyncStorage.getItem('selectedLanguage');
      i18n.changeLanguage(storedLanguage as LanguageInitial);
      // setMomentLocale(storedLanguage as LanguageInitial);
      return storedLanguage ? (storedLanguage as LanguageInitial) : 'en';
    } catch (error) {
      console.error('Failed to load language from AsyncStorage:', error);
      // setMomentLocale('en');
      return 'en'; // Fallback to 'en' if there's an error
    }
  }
);

// Async thunk to set the language and update AsyncStorage
export const setLanguage = createAsyncThunk(
  'language/setLanguage',
  async (language: LanguageInitial, { dispatch }) => {
    try {
      await AsyncStorage.setItem('selectedLanguage', language);
      i18n.changeLanguage(language);
      // setMomentLocale(language as LanguageInitial)
      return language;
    } catch (error) {
      console.error('Failed to set language:', error);
      // setMomentLocale('en');
      return language; // Fallback to provided language in case of error
    }
  }
);

export const LanguageSlice = createSlice({
  name: 'language',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(loadLanguage.fulfilled, (state, action) => {
      state.selectedLanguage = action.payload;
    });
    builder.addCase(setLanguage.fulfilled, (state, action) => {
      state.selectedLanguage = action.payload;
    });
  },
});

export default LanguageSlice;
