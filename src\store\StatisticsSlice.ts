import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {IWeight} from '../interfaces/IWeight';
import {IWater} from '../interfaces/IWater';

interface IStatisticsInitialState {
  weights: IWeight[];
  waterIntake: IWater;
}

const initialState: IStatisticsInitialState = {
  weights: [],
  waterIntake: {
    _id: '',
    client: '',
    createdAt: '',
    notes: '',
    unit: 'ltr',
    updatedAt: '',
    value: 0,
  },
};

// SLICE
export const StatisticsSlice = createSlice({
  name: 'statistics',
  initialState,
  reducers: {
    setWeights: (state, {payload}: PayloadAction<Array<IWeight>>) => {
      state.weights = payload;
      return state;
    },
    addNewWeight: (state, {payload}: PayloadAction<IWeight>) => {
      state.weights = [...state.weights, payload];
      return state;
    },
    setWaterIntake: (state, {payload}: PayloadAction<IWater>) => {
      state.waterIntake = payload;
      return state;
    },
    resetStats: state => {
      state = initialState;
      return state;
    },
  },
});

// ACTIONS
export const {setWeights, setWaterIntake, addNewWeight, resetStats} =
  StatisticsSlice.actions;

// REDUCER
export default StatisticsSlice;
