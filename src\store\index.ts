import {configureStore} from '@reduxjs/toolkit';
import {
  useDispatch,
  useSelector as useReduxSelector,
  TypedUseSelectorHook,
} from 'react-redux';
import {persistStore, persistReducer} from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import UserSlice from './userSlice';
import MealsSlice from './MealsSlice';
import FoodsSlice from './FoodsSlice';
import StatisticsSlice from './StatisticsSlice';
import DocumentsSlice from './DocumentsSlice';
import LanguagesSlice from './LanguagesSlice';
import ChatsSlice from './chatsSlice';
import MealPostsSlice from './mealPostSlice';
import ClientsSlice from './clientsSlice';

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  blacklist: [],
};

const persistedReducer = persistReducer(persistConfig, UserSlice.reducer);

// SLICES
const store = configureStore({
  reducer: {
    user: persistedReducer,
    meals: MealsSlice.reducer,
    foods: FoodsSlice.reducer,
    statistics: StatisticsSlice.reducer,
    documents: DocumentsSlice.reducer,
    language: LanguagesSlice.reducer,
    chats: ChatsSlice.reducer,
    mealPosts: MealPostsSlice.reducer,
    clients: ClientsSlice.reducer,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

const persistor = persistStore(store);

export {store, persistor};

export type RootState = ReturnType<typeof store.getState>;

export type AppDispatch = typeof store.dispatch;
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useReduxSelector;

export default store;
