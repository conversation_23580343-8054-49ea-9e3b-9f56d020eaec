import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {IMealPost} from '../interfaces/IMeal';

interface IMealPostsInitialState {
  mealPosts: IMealPost[];
  isLoading: boolean;
}

const initialState: IMealPostsInitialState = {
  mealPosts: [],
  isLoading: false,
};

// SLICE
export const MealPostsSlice = createSlice({
  name: 'mealPosts',
  initialState,
  reducers: {
    setMealPostsAction: (state, {payload}: PayloadAction<Array<IMealPost>>) => {
      state.mealPosts = payload;
      return state;
    },
    updateMealPostAction: (
      state,
      {payload}: PayloadAction<Partial<IMealPost>>,
    ) => {
      let index = state.mealPosts.findIndex(item => item._id === payload._id);
      state.mealPosts[index] = {...state.mealPosts[index], ...payload};
      return state;
    },
    updateLoadingStatus: (state, {payload}: PayloadAction<boolean>) => {
      state.isLoading = payload;
      return state;
    },
    resetMealPosts: state => {
      state = initialState;
      return state;
    },
  },
});

// ACTIONS
export const {
  setMealPostsAction,
  updateMealPostAction,
  updateLoadingStatus,
  resetMealPosts,
} = MealPostsSlice.actions;

// REDUCER
export default MealPostsSlice;
