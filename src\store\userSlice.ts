import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {IGoal, INutrients, IUser} from '../interfaces/IUser.';
import {IClientGoal} from '../services/ClientServices';
import {IActivity} from '../interfaces/IActivity';

interface iInitialState {
  user: IUser;
  firstLaunch: boolean;
  dailyIntakeGoal: IGoal;
  dailyMacroNutrients: INutrients;
  stepCount?: number;
  dailyActivity?: IActivity;
}

const initialState: iInitialState = {
  user: {
    _id: '',
    name: '',
    email: '',
    experience: '',
    about: '',
    photo: '',
    code: '',
    verificationCodeExpiresAt: 0,
    dob: '',
    referralCode: '',
    gender: '',
    height: '',
    weight: '',
    goalWeight: '',
    howActive: '',
    archived: false,
    deleted: false,
    image: '',
    photos: [],
    createdAt: null,
    updatedAt: null,
    userType: 'client',
  },
  dailyIntakeGoal: {
    _id: '',
    calories: 0,
    carbs: 0,
    carbsPercentage: 0,
    client: '',
    coach: '',
    createdAt: '',
    fat: 0,
    fatPercentage: 0,
    proteins: 0,
    proteinsPercentage: 0,
    updatedAt: '',
  },
  dailyMacroNutrients: {
    usedCarbs: 0,
    usedCalories: 0,
    usedFats: 0,
    usedProtiens: 0,
  },
  firstLaunch: true,
  stepCount: 0,
};

// SLICE
export const UserSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, {payload}: PayloadAction<IUser>) => {
      state.user = {...payload};
      return state;
    },
    resetUser: state => {
      state.user = initialState.user;
      return state;
    },
    setDailyGoal: (state, {payload}: PayloadAction<IGoal>) => {
      state.dailyIntakeGoal = payload;
      return state;
    },
    setDailyActivity: (state, {payload}: PayloadAction<IActivity>) => {
      state.dailyActivity = payload;
      return state;
    },
    setDailyNutrients: (state, {payload}) => {
      state.dailyMacroNutrients = payload;
      return state;
    },
    updateDailyActivity: (state, {payload}) => {
      return {
        ...state,
        dailyActivity: {
          ...state.dailyActivity,
          ...payload,
        },
      };
    },
    updateUser: (state, {payload}) => {
      return {
        ...state,
        user: {
          ...state.user,
          ...payload,
        },
      };
    },
    setCoachInfo: (state, {payload}) => {
      state.user.coach = payload;
      return state;
    },
    setFirstLaunch: (state, action) => {
      state.firstLaunch = action.payload;
    },
    setStepCount: (state, action) => {
      state.stepCount = action.payload;
    },
  },
});

// ACTIONS
export const {
  setUser,
  resetUser,
  updateUser,
  setFirstLaunch,
  setDailyGoal,
  setDailyNutrients,
  setCoachInfo,
  setStepCount,
  setDailyActivity,
  updateDailyActivity,
} = UserSlice.actions;

// REDUCER
export default UserSlice;
