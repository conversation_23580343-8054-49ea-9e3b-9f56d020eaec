import {
  CALORIES_PER_CARB,
  CALORIES_PER_FAT,
  CALORIES_PER_PROTEIN,
} from './constants';

interface ICaloriesPayload {
  proteins: number;
  carbs: number;
  fat: number;
}

export function calculateCalories(payload: ICaloriesPayload) {
  let calInProteins = payload.proteins * CALORIES_PER_PROTEIN;
  let calInCarbs = payload.carbs * CALORIES_PER_CARB;
  let calInFat = payload.fat * CALORIES_PER_FAT;

  let sum = calInProteins + calInCarbs + calInFat;

  let proteinsPercentage = Math.round((calInProteins / sum) * 100);
  let carbsPercentage = Math.round((calInCarbs / sum) * 100);
  let fatPercentage = Math.round((calInFat / sum) * 100);

  let totalPercentage = proteinsPercentage + carbsPercentage + fatPercentage;
  let difference = 100 - totalPercentage;

  // CORRECT THE LARGEST VALUE BY DIFFERENCE TO ENSURE SUM = 100
  if (difference !== 0) {
    if (
      proteinsPercentage >= carbsPercentage &&
      proteinsPercentage >= fatPercentage
    ) {
      proteinsPercentage += difference;
    } else if (
      carbsPercentage >= proteinsPercentage &&
      carbsPercentage >= fatPercentage
    ) {
      carbsPercentage += difference;
    } else {
      fatPercentage += difference;
    }
  }

  return {
    sum,
    proteinsPercentage,
    carbsPercentage,
    fatPercentage,
  };
}

export interface IGoalCalculator {
  weight: number;
  gender: 'male' | 'female';
  howActive:
    | 'Sedentary'
    | 'Lightly Active'
    | 'Moderately Active'
    | 'Highly Active';
  goal: 'Lose Weight' | 'Maintain Weight' | 'Gain Weight';
}

export function goalCalculator(payload: IGoalCalculator) {
  const {gender, goal, howActive, weight} = payload;
  let userMultiplier: 13 | 14 | 15 | 16 | 17 = 14;

  // FOR MEN - MULTIPLIER
  if (gender === 'male') {
    switch (howActive) {
      case 'Sedentary':
        userMultiplier = 14;
        break;
      case 'Lightly Active':
        userMultiplier = 15;
        break;
      case 'Moderately Active':
        userMultiplier = 16;
        break;
      case 'Highly Active':
        userMultiplier = 17;
        break;
      default:
        break;
    }
  }

  // FOR WOMEN - MULTIPLIER
  if (gender === 'female') {
    switch (howActive) {
      case 'Sedentary':
        userMultiplier = 13;
        break;
      case 'Lightly Active':
        userMultiplier = 14;
        break;
      case 'Moderately Active':
        userMultiplier = 15;
        break;
      case 'Highly Active':
        userMultiplier = 16;
        break;
      default:
        break;
    }
  }

  // THIS IS THE AVERAGE COLORIE INTAKE/CONSUMPTION OF THAT PERSON
  let calorieMaintenance = weight * userMultiplier;

  let targetCalorieConsumption = 0;

  // FOR MEN - TARGET CALORIES CONSUMPTION
  if (gender === 'male') {
    switch (goal) {
      case 'Gain Weight':
        targetCalorieConsumption = calorieMaintenance + 500;
        break;
      case 'Lose Weight':
        targetCalorieConsumption = calorieMaintenance - 500;
        break;
      case 'Maintain Weight':
        targetCalorieConsumption = calorieMaintenance;
        break;
      default:
        break;
    }
  }

  // FOR WOMEN - TARGET CALORIES CONSUMPTION
  if (gender === 'female') {
    switch (goal) {
      case 'Gain Weight':
        targetCalorieConsumption = calorieMaintenance + 250;
        break;
      case 'Lose Weight':
        targetCalorieConsumption = calorieMaintenance - 250;
        break;
      case 'Maintain Weight':
        targetCalorieConsumption = calorieMaintenance;
        break;
      default:
        break;
    }
  }
  // NUTRIENTS CALCULATION
  let nutrients = calculateMacros(goal, targetCalorieConsumption);

  return {
    calories: targetCalorieConsumption,
    ...nutrients,
  };
}

export function calculateMacros(
  goal: 'Lose Weight' | 'Maintain Weight' | 'Gain Weight',
  calories: number,
) {
  let proteinsPercentage = 0;
  let carbsPercentage = 0;
  let fatPercentage = 0;

  switch (goal) {
    case 'Gain Weight':
      proteinsPercentage = 0.25; // 25%
      carbsPercentage = 0.5; // 50%
      fatPercentage = 0.25; // 25%
      break;
    case 'Lose Weight':
      proteinsPercentage = 0.3; // 30%
      carbsPercentage = 0.4; // 40%
      fatPercentage = 0.3; // 30%
      break;
    case 'Maintain Weight':
      break;
    default:
      break;
  }

  // Ensure percentages sum to 100
  const totalPercentage =
    Math.round(proteinsPercentage * 100) +
    Math.round(carbsPercentage * 100) +
    Math.round(fatPercentage * 100);
  const adjustment = totalPercentage - 100;

  if (adjustment !== 0) {
    if (Math.abs(adjustment) <= 2) {
      // Simple fix for small adjustments
      if (adjustment > 0) {
        if (
          proteinsPercentage > carbsPercentage &&
          proteinsPercentage > fatPercentage
        ) {
          proteinsPercentage -= adjustment / 100;
        } else if (carbsPercentage > fatPercentage) {
          carbsPercentage -= adjustment / 100;
        } else {
          fatPercentage -= adjustment / 100;
        }
      } else {
        if (
          proteinsPercentage < carbsPercentage &&
          proteinsPercentage < fatPercentage
        ) {
          proteinsPercentage -= adjustment / 100;
        } else if (carbsPercentage < fatPercentage) {
          carbsPercentage -= adjustment / 100;
        } else {
          fatPercentage -= adjustment / 100;
        }
      }
    } else {
      // More complex adjustment for larger deviations, redistributing proportionally
      const factor = (100 - adjustment) / 100;
      proteinsPercentage *= factor;
      carbsPercentage *= factor;
      fatPercentage *= factor;
    }
  }

  // Calories from each macronutrient
  const proteinCalories = Math.round(calories * proteinsPercentage);
  const carbsCalories = Math.round(calories * carbsPercentage);
  const fatCalories = Math.round(calories * fatPercentage);

  // Grams of each macronutrient
  const proteinGrams = Math.round(proteinCalories / 4);
  const carbsGrams = Math.round(carbsCalories / 4);
  const fatGrams = Math.round(fatCalories / 9);

  return {
    proteins: proteinGrams,
    proteinsPercentage: Math.round(proteinsPercentage * 100),
    carbs: carbsGrams,
    carbsPercentage: Math.round(carbsPercentage * 100),
    fat: fatGrams,
    fatPercentage: Math.round(fatPercentage * 100),
  };
}

export function kgToLbs(kg: number) {
  const poundsPerKg = 2.20462;
  let inKgs = kg * poundsPerKg;
  return Math.round(inKgs);
}

export const netCaloriesLeft = (
  dailyIntakeGoal: any,
  dailyMacroNutrients: any,
) => {
  if (dailyIntakeGoal?.calories < dailyMacroNutrients?.usedCalories) {
    return 0;
  } else if (dailyIntakeGoal?.calories) {
    return Math.trunc(
      dailyIntakeGoal?.calories - dailyMacroNutrients?.usedCalories,
    );
  } else {
    return 0;
  }
};

export const netCaloriePercentage = (
  dailyIntakeGoal: any,
  dailyMacroNutrients: any,
) => {
  if (dailyIntakeGoal?.calories < dailyMacroNutrients?.usedCalories) {
    return 100;
  }
  if (dailyMacroNutrients.usedCalories) {
    return (
      (dailyMacroNutrients?.usedCalories / dailyIntakeGoal?.calories) * 100
    );
  } else {
    return 0;
  }
};
