import {createTheme, lightColors, darkColors} from '@rneui/themed';
import {Dimensions, Platform, StatusBar} from 'react-native';

export const theme = createTheme({
  lightColors: {
    ...Platform.select({
      default: lightColors.platform.android,
      ios: lightColors.platform.ios,
    }),
    // primary: '#000',
    primary: '#000000',
    secondary: '#2C2C2E',
    grey1: '#ECECEC',
    grey2: '#DDDDDD',
    grey3: '#E5E5E5',
    grey4: '#A7ABAE',
    grey5: '#FAFAFA',
    grey6: '#F3F3F4',
    grey7: '#969696',
    background: '#FDFDFD',
    searchBg: '#51A5F3',
    placeholderColor: '#2C2C2E66',
    error: '#F64144',
    blue: '#195FE5',
  },
  darkColors: {
    primary: 'orange',
    background: darkColors.background,
    placeholderColor: '#2C2C2E66',
    error: '#F64144',
  },
  // mode: 'dark',
});

const screenHeight = Dimensions.get('window').height;
export const sizes = {
  paddingTop: Platform.OS === 'android' ? 0 : 0,
  adjustedHeight: screenHeight * 0.09,
  deviceWidth: Dimensions.get('screen').width,
  deviceHeight: Dimensions.get('screen').height,
};
export const Fonts = {
  regular: 'Inter-Regular',
  medium: 'Inter-Medium',
  semiBold: 'Inter-SemiBold',
  bold: 'Inter-Bold',
  light: 'Inter-Light-BETA',
};
