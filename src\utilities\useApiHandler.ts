import axios, {AxiosError} from 'axios';
import {useTranslation} from 'react-i18next';
import Toast from 'react-native-toast-message';

export default () => {
  const {t} = useTranslation();

  function handleAxiosErrors(error: AxiosError) {
    // INTERNET NOT WORKING
    if (error.code === 'ERR_NETWORK') {
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Make sure your internet is working.'),
      });
      return;
    }
    // GENERAL ERROR
    else {
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Something went wrong.'),
      });
    }
  }

  function handleApiErrors(error: any) {
    if (axios.isAxiosError(error)) {
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: mapErrorToMessage(error),
      });
      return;
    }
    // INTERNET NOT WORKING
    if (error.code === 'ERR_NETWORK') {
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Make sure your internet is working.'),
      });
      return;
    }
    // GENERAL ERROR
    else {
      showErrorToast(t('Something went wrong.'));
    }
  }

  const showErrorToast = (message?: string) => {
    Toast.show({
      type: 'error',
      text1: t('Error'),
      text2: message,
    });
  };

  function mapErrorToMessage(error: AxiosError) {
    const data: any = error.response?.data;
    if (typeof data === 'string') {
      return data;
    }
    if (typeof data === 'object') {
      if (typeof data?.message === 'string') return data?.message;

      if (Array.isArray(data?.message)) return data.message[0];
    }
    return t('Something went wrong...');
  }

  return {
    showErrorToast,
    mapErrorToMessage,
    handleApiErrors,
    handleAxiosErrors,
  };
};
